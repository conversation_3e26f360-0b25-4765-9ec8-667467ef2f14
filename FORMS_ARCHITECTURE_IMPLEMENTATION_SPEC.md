# Forms Architecture Implementation Specification

## Overview
This document specifies the complete implementation of the forms architecture for the Indi Onboarding system, ensuring proper React Hook Form integration, user data population, and signature handling.

## Core Requirements

### 1. React Hook Form Integration
- ✅ **Status**: COMPLETE - All 11 forms use `useBaseForm` hook
- **Implementation**: All forms must use React Hook Form as the single source of truth
- **Forms**: broker-information, business-card, mpc-application, payment-authorization, photos, website-information, contract-and-schedule, policies, letter-of-direction, unlicensed-information, unlicensed-policies

### 2. User Data Population Logic
- ❌ **Status**: NEEDS IMPLEMENTATION
- **Requirements**:
  - If `firstSaveComplete: false` → populate form with user data
  - If `firstSaveComplete: true` → populate form with saved form data
  - On first save → set `firstSaveComplete: true`

### 3. Photos Form Bidirectional User Integration
- ❌ **Status**: NEEDS IMPLEMENTATION
- **Special Requirements**:
  - Check if `user.photo` and `user.printPhoto` exist
  - Pre-populate form with existing user photos
  - On photo upload: update user profile with `refId` and `field` parameters
  - On form save: sync photo IDs between form and user profile

### 4. Signature Forms Flow
- ❌ **Status**: NEEDS IMPLEMENTATION
- **Forms with Signatures**: mpc-application, contract-and-schedule, policies, unlicensed-information, unlicensed-policies
- **Flow Requirements**:
  - Check if `user.signature` exists
  - If NO user signature: Show `SignatureCapture` component
  - If YES user signature: Show `OneClickSignature` component
  - If form already signed: Show `SavedSignatureDisplay` component
  - Save button disabled until signature is saved (when `firstSaveComplete: false`)

## Implementation Plan

### Phase 1: Core Infrastructure Fixes

#### Fix #1: Correct firstSaveComplete Logic in useBaseForm
**File**: `src/shared/hooks/use-base-form.ts`
**Issue**: Currently always sets `firstSaveComplete: true` regardless of actual status
**Solution**: Check existing form data to determine if this is first save

```typescript
const onSubmit = React.useCallback(async (data: T) => {
  // Get current form data to check firstSaveComplete status
  const currentFormData = forms[formName];
  const isFirstSave = !currentFormData?.firstSaveComplete;
  
  const formDataToSubmit = {
    ...submitData,
    isFormComplete: true,
    firstSaveComplete: true, // Set to true on ANY save
    lastUpdated: new Date().toISOString(),
    wasFirstSave: isFirstSave, // For debugging
  };
  
  // ... rest of implementation
}, [formName, forms, saveFormData, form, transformSubmitData, onSuccess, onError]);
```

#### Fix #2: Create Enhanced Data Population Hook
**File**: `src/shared/hooks/use-form-data-population.ts` (NEW FILE)
**Purpose**: Handle conditional population based on `firstSaveComplete` status

```typescript
export function useFormDataPopulation<T extends FieldValues>(
  formName: keyof OnboardingForms,
  form: UseFormReturn<T>,
  options: FormDataPopulationOptions = {}
) {
  const { forms } = useFormsContext();
  const { userAuth } = useAuthContext();

  React.useEffect(() => {
    const formData = forms[formName];
    const userData = userAuth.userInfo;
    
    if (!userData) return;

    if (!formData?.firstSaveComplete) {
      // First time: populate with user data
      if (!options.skipUserPopulation && options.userFieldMappings) {
        populateWithUserData(form, userAuth, options.userFieldMappings);
      }
      
      if (options.customPopulationLogic) {
        options.customPopulationLogic(formData, userData, form);
      }
    } else if (formData?.firstSaveComplete && formData) {
      // Subsequent times: populate with saved form data
      const formFields = Object.keys(form.getValues());
      formFields.forEach(field => {
        if (formData[field] !== undefined && formData[field] !== null) {
          form.setValue(field as any, formData[field]);
        }
      });
    }
  }, [forms[formName]?.firstSaveComplete, userAuth.userInfo?.id, form, formName]);
}
```

### Phase 2: Photos Form Special Implementation

#### Fix #3: Photos Form Bidirectional User Integration
**File**: `src/features/photos/hooks/use-photos-form.ts`
**Requirements**:
1. Check user.photo and user.printPhoto on load
2. Pre-populate form with existing photos
3. Upload new photos with refId and field parameters
4. Update user profile when photos are uploaded
5. Sync IDs between form and user on save

**Key Implementation Points**:
- Custom population logic for photos
- Enhanced file upload with user profile integration
- Success callback to update user profile
- Bidirectional sync between form and user data

### Phase 3: User Data Population for All Forms

#### Fix #4: Add User Data Population to Missing Forms
**Forms to Update**:
- `business-card` - Add basic user field mappings
- `website-information` - Add website-related user fields
- `contract-and-schedule` - Add contract-related user fields  
- `letter-of-direction` - Add inheritance from broker info

**Template for Each Form**:
```typescript
// Add to each form hook
const userFieldMappings = {
  // Define relevant user field mappings
};

// Use enhanced data population
useFormDataPopulation(formName, baseForm.form, {
  userFieldMappings,
});
```

### Phase 4: Signature Flow Implementation

#### Fix #5: Fix Signature Flow Logic
**Forms to Update**: mpc-application, contract-and-schedule, policies, unlicensed-information, unlicensed-policies

**Correct Logic Template**:
```typescript
const isSignatureRequiredAndSaved = React.useMemo(() => {
  const formData = forms[formName];
  const hasSignature = !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
  
  // If form hasn't been saved yet, require signature
  if (!formData?.firstSaveComplete) {
    return hasSignature;
  }
  
  // If form has been saved, always allow submission
  return true;
}, [forms[formName]?.firstSaveComplete, watchedValues.signature]);
```

#### Fix #6: Integrate SignatureSection Components
**Files to Update**: All signature form components
**Component Integration**:
```typescript
<SignatureSection
  value={watchedValues.signature}
  onSignatureChange={(signature) => setValue('signature', signature)}
  setValue={setValue}
  form={form}
  fieldName="signature"
  formName={formName}
  label="Your Signature"
  required={true}
  allowOneClick={true}
  showProfileSignature={true}
/>
```

## File Structure

### New Files to Create
- `src/shared/hooks/use-form-data-population.ts`

### Files to Modify

#### Core Infrastructure
- `src/shared/hooks/use-base-form.ts`

#### Forms Hooks
- `src/features/photos/hooks/use-photos-form.ts` (Major changes)
- `src/features/business-card/hooks/use-business-card-form.ts`
- `src/features/website-information/hooks/use-website-info-form.ts`
- `src/features/contract-and-schedule/hooks/use-contract-form.ts`
- `src/features/letter-of-direction/hooks/use-letter-of-direction-form.ts`
- `src/features/policies/hooks/use-policies-form.ts`
- `src/features/unlicensed-information/hooks/use-unlicensed-info-form.ts`
- `src/features/unlicensed-policies/hooks/use-unlicensed-policies-form.ts`
- `src/features/mpc-application/hooks/use-mpc-application-form.ts`

#### Forms Components (for signature integration)
- `src/features/policies/components/policies-form.tsx`
- `src/features/unlicensed-information/components/unlicensed-information-form.tsx`
- `src/features/unlicensed-policies/components/unlicensed-policies-form.tsx`
- `src/features/mpc-application/components/mpc-application-form.tsx`
- `src/features/contract-and-schedule/components/contract-and-schedule-form.tsx`

## Success Criteria

### Phase 1 Success Criteria
- [ ] `useBaseForm` correctly handles `firstSaveComplete` logic
- [ ] `useFormDataPopulation` hook created and functional
- [ ] All forms can differentiate between first save and subsequent saves

### Phase 2 Success Criteria  
- [ ] Photos form populates with existing user photos
- [ ] Photo uploads update user profile with correct refId/field
- [ ] Form save syncs photo IDs between form and user profile
- [ ] Bidirectional sync working correctly

### Phase 3 Success Criteria
- [ ] All 11 forms have proper user data population
- [ ] First load populates with user data (when firstSaveComplete: false)
- [ ] Subsequent loads populate with saved form data (when firstSaveComplete: true)

### Phase 4 Success Criteria
- [ ] All signature forms show correct signature state
- [ ] Save button properly disabled until signature saved
- [ ] SignatureCapture shown when no user signature exists
- [ ] OneClickSignature shown when user signature exists
- [ ] SavedSignatureDisplay shown when form already signed
- [ ] Signature components properly integrated in all signature forms

## Testing Checklist

### User Data Population Testing
- [ ] New user (no saved forms): Forms populate with user profile data
- [ ] Returning user (saved forms): Forms populate with saved form data
- [ ] Mixed scenario: Some forms saved, others not - each behaves correctly

### Photos Form Testing
- [ ] New user (no photos): Empty form, can upload photos
- [ ] User with existing photos: Form pre-populated with existing photos
- [ ] Photo upload: Updates both form and user profile
- [ ] Form save: Syncs photo IDs correctly

### Signature Flow Testing
- [ ] New user (no signature): Shows SignatureCapture component
- [ ] User with signature: Shows OneClickSignature component
- [ ] Form already signed: Shows SavedSignatureDisplay component
- [ ] Save button: Disabled until signature saved (first save only)
- [ ] One-click signing: Works correctly with existing user signature

### Integration Testing
- [ ] All 11 forms work correctly with new architecture
- [ ] No race conditions in form saving
- [ ] No dual state management issues
- [ ] Build passes without errors
- [ ] TypeScript compilation successful

## Implementation Order

1. **Phase 1**: Core infrastructure fixes (useBaseForm, data population hook)
2. **Phase 2**: Photos form special implementation
3. **Phase 3**: User data population for all forms
4. **Phase 4**: Signature flow implementation
5. **Testing**: Comprehensive testing of all scenarios
6. **Validation**: Build, lint, and functionality verification

## Notes

- Maintain backward compatibility where possible
- Preserve existing form validation logic
- Ensure proper error handling throughout
- Add comprehensive logging for debugging
- Document any breaking changes
- Test thoroughly before marking as complete

---

**Document Version**: 1.0  
**Created**: December 2024  
**Last Updated**: December 2024  
**Status**: Implementation In Progress
