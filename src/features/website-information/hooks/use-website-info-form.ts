"use client";

import * as React from "react";
import { websiteInfoSchema, WebsiteInfoFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { toast } from "@/shared/lib/toast";

export function useWebsiteInfoForm() {
  // Transform function for website-specific validation
  const transformSubmitData = React.useCallback((data: WebsiteInfoFormData) => {
    // Validate required fields
    if (data.websiteOptIn === undefined) {
      throw new Error("Please select whether you want an Indi Website");
    }

    if (data.priorWebsite === undefined) {
      throw new Error("Please select whether you have existing mortgage websites");
    }

    return data;
  }, []);

  // Use the base form hook
  const baseForm = useBaseForm<WebsiteInfoFormData>({
    formName: 'websiteInfo',
    schema: websiteInfoSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: {
      websiteOptIn: undefined,
      ownDomain: false,
      providedDomain: false,
      websiteDomainName: "",
      websiteDomainRegistrar: "",
      priorWebsite: undefined,
      priorWebsitesUse: [],
    },
    transformSubmitData,
  });

  const { form, watchedValues, errors } = baseForm;
  const { setValue } = form;

  // User field mappings for website-related fields
  const userFieldMappings = {
    // Add any relevant user fields that should populate website form
    // websiteDomainName: 'website', // if user has a website field
    // Note: Most website info is form-specific, so limited user data population
  };

  // Use enhanced data population
  useFormDataPopulation('websiteInfo', baseForm.form, {
    userFieldMappings,
  });

  // Handle domain type selection (mutually exclusive)
  const handleDomainTypeChange = (type: 'own' | 'provided', checked: boolean) => {
    if (checked) {
      if (type === 'own') {
        setValue('ownDomain', true);
        setValue('providedDomain', false);
      } else {
        setValue('ownDomain', false);
        setValue('providedDomain', true);
      }
    } else {
      setValue(type === 'own' ? 'ownDomain' : 'providedDomain', false);
    }
  };

  // Add new prior website
  const addPriorWebsite = () => {
    const currentWebsites = watchedValues.priorWebsitesUse || [];
    setValue('priorWebsitesUse', [...currentWebsites, { domain: '', keepInUse: undefined, redirect: undefined }]);
  };

  // Remove prior website
  const removePriorWebsite = (index: number) => {
    const currentWebsites = watchedValues.priorWebsitesUse || [];
    setValue('priorWebsitesUse', currentWebsites.filter((_, i) => i !== index));
  };

  // Update prior website field
  const updatePriorWebsite = (index: number, field: string, value: any) => {
    const currentWebsites = watchedValues.priorWebsitesUse || [];
    const updatedWebsites = [...currentWebsites];
    updatedWebsites[index] = { ...updatedWebsites[index], [field]: value };
    setValue('priorWebsitesUse', updatedWebsites);
  };

  return {
    // Base form functionality
    ...baseForm,

    // Website-specific handlers
    handleDomainTypeChange,
    addPriorWebsite,
    removePriorWebsite,
    updatePriorWebsite,
  };
}
