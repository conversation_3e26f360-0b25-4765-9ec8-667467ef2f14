"use client";

import * as React from "react";
import { photosSchema, PhotosFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { api } from "@/shared/lib/api";
import { toast } from "@/shared/lib/toast";

export function usePhotosForm() {
  const { userAuth, updateUserData } = useAuthContext();
  const [digitalPhotoPreview, setDigitalPhotoPreview] = React.useState<string | null>(null);
  const [printPhotoPreview, setPrintPhotoPreview] = React.useState<string | null>(null);
  const [dragActive, setDragActive] = React.useState({ digital: false, print: false });
  const [files, setFiles] = React.useState<{ digital?: File, print?: File }>({});

  // Transform function for photos-specific data processing
  const transformSubmitData = React.useCallback((data: PhotosFormData) => {
    // Validate that either a photo is uploaded or default photo is selected
    if (!data.useDefaultPhoto && !data.photo) {
      throw new Error("Please upload a digital photo or select the default photo option");
    }
    return data;
  }, []);

  // Success callback to update user profile when photos are saved
  const onSuccess = React.useCallback(async (data: PhotosFormData) => {
    try {
      // CRITICAL: Update user profile with photo IDs if they were uploaded
      const userUpdateData: any = {};
      
      if (data.photo && typeof data.photo === 'number') {
        userUpdateData.photo = data.photo;
      }
      
      if (data.printPhoto && typeof data.printPhoto === 'number') {
        userUpdateData.printPhoto = data.printPhoto;
      }
      
      if (Object.keys(userUpdateData).length > 0) {
        await updateUserData(userUpdateData);
        console.log('📸 Updated user profile with photo IDs:', userUpdateData);
        toast.success('Photos saved and synced to your profile!');
      }
    } catch (error) {
      console.error('Error updating user profile with photos:', error);
      // Don't fail the form submission if user update fails
      toast.warning('Photos saved, but profile sync failed');
    }
  }, [updateUserData]);

  // Use the base form hook with photos-specific configuration
  const baseForm = useBaseForm<PhotosFormData>({
    formName: 'photos',
    schema: photosSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: {
      photo: "",
      printPhoto: "",
      useDefaultPhoto: false,
    },
    transformSubmitData,
    onSuccess,
  });

  const { form, watchedValues } = baseForm;
  const { setValue } = form;

  // CRITICAL: Custom population logic for photos form bidirectional user integration
  const customPhotosPopulation = React.useCallback((formData: any, userData: any, form: any) => {
    console.log('📸 Photos form: Custom population logic triggered', { formData, userData });
    
    // Check if user has existing photos and populate form
    if (userData.photo) {
      const photoValue = typeof userData.photo === 'object' ? userData.photo.id : userData.photo;
      const photoUrl = typeof userData.photo === 'object' ? userData.photo.url : null;
      
      setValue('photo', photoValue);
      if (photoUrl) {
        setDigitalPhotoPreview(photoUrl);
        console.log('📸 Populated digital photo from user profile:', { id: photoValue, url: photoUrl });
      }
    }
    
    if (userData.printPhoto) {
      const printPhotoValue = typeof userData.printPhoto === 'object' ? userData.printPhoto.id : userData.printPhoto;
      const printPhotoUrl = typeof userData.printPhoto === 'object' ? userData.printPhoto.url : null;
      
      setValue('printPhoto', printPhotoValue);
      if (printPhotoUrl) {
        setPrintPhotoPreview(printPhotoUrl);
        console.log('📸 Populated print photo from user profile:', { id: printPhotoValue, url: printPhotoUrl });
      }
    }
    
    // If user has no photos, ensure form starts clean
    if (!userData.photo && !userData.printPhoto) {
      console.log('📸 User has no existing photos, starting with clean form');
    }
  }, [setValue]);

  // Use enhanced data population with custom logic
  useFormDataPopulation('photos', form, {
    skipUserPopulation: true, // We handle this manually due to special photo logic
    customPopulationLogic: customPhotosPopulation,
  });

  // Update previews when form values change
  React.useEffect(() => {
    if (watchedValues.photo && watchedValues.photo !== digitalPhotoPreview) {
      // If it's a number (ID), we need to fetch the URL for preview
      if (typeof watchedValues.photo === 'number') {
        const fetchPhotoUrl = async () => {
          try {
            const response = await api.get(`/upload/files/${watchedValues.photo}`);
            if (response.success && response.data && (response.data as any).url) {
              setDigitalPhotoPreview((response.data as any).url);
            }
          } catch (error) {
            console.error('Error fetching photo URL:', error);
          }
        };
        fetchPhotoUrl();
      } else if (typeof watchedValues.photo === 'string') {
        setDigitalPhotoPreview(watchedValues.photo);
      }
    }
    
    if (watchedValues.printPhoto && watchedValues.printPhoto !== printPhotoPreview) {
      // If it's a number (ID), we need to fetch the URL for preview
      if (typeof watchedValues.printPhoto === 'number') {
        const fetchPrintPhotoUrl = async () => {
          try {
            const response = await api.get(`/upload/files/${watchedValues.printPhoto}`);
            if (response.success && response.data && (response.data as any).url) {
              setPrintPhotoPreview((response.data as any).url);
            }
          } catch (error) {
            console.error('Error fetching print photo URL:', error);
          }
        };
        fetchPrintPhotoUrl();
      } else if (typeof watchedValues.printPhoto === 'string') {
        setPrintPhotoPreview(watchedValues.printPhoto);
      }
    }
  }, [watchedValues.photo, watchedValues.printPhoto]);

  // Validate image requirements
  const verifyImageRequirements = (file: File, type: 'digital' | 'print'): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const width = img.naturalWidth;
        const height = img.naturalHeight;

        // Check minimum dimensions (500x500 pixels)
        if (width < 500 || height < 500) {
          toast.error(`Image must be at least 500x500 pixels. Current size: ${width}x${height} pixels.`);
          resolve(false);
          return;
        }

        resolve(true);
      };

      img.onerror = () => {
        toast.error('Unable to analyze image. Please ensure it is a valid image file.');
        resolve(false);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // CRITICAL: Enhanced file upload handler with user profile integration
  const handleFileSelect = async (file: File, type: 'digital' | 'print') => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file (JPG or PNG)');
      return;
    }

    // Validate file size (15MB limit)
    if (file.size > 15 * 1024 * 1024) {
      toast.error('File size must be less than 15MB');
      return;
    }

    // Verify image requirements
    const isValid = await verifyImageRequirements(file, type);
    if (!isValid) return;

    try {
      // CRITICAL: Upload to backend with refId and field parameters for user profile integration
      const formData = new FormData();
      formData.append('files', file);
      formData.append('refId', String(userAuth.userInfo?.id));
      formData.append('ref', 'plugin::users-permissions.user');
      formData.append('field', type === 'digital' ? 'photo' : 'printPhoto');

      console.log('📸 Uploading photo with user profile integration:', {
        type,
        userId: userAuth.userInfo?.id,
        field: type === 'digital' ? 'photo' : 'printPhoto'
      });

      // Upload to backend
      const response = await api.post('/upload', formData);

      if (response.success && response.data && Array.isArray(response.data) && response.data[0]) {
        const uploadData = response.data[0] as any;
        
        // Update form field with upload ID (not URL)
        const fieldName = type === 'digital' ? 'photo' : 'printPhoto';
        setValue(fieldName as keyof PhotosFormData, uploadData.id);
        
        // Update preview with URL
        if (type === 'digital') {
          setDigitalPhotoPreview(uploadData.url);
        } else {
          setPrintPhotoPreview(uploadData.url);
        }
        
        // Store file reference
        setFiles(prev => ({ ...prev, [type]: file }));
        
        console.log(`📸 Successfully uploaded ${type} photo:`, uploadData);
        toast.success(`${type === 'digital' ? 'Digital' : 'Print'} photo uploaded successfully`);
        
        // CRITICAL: Update user profile immediately with new photo
        const userUpdateData = {
          [type === 'digital' ? 'photo' : 'printPhoto']: uploadData.id
        };
        
        try {
          await updateUserData(userUpdateData);
          console.log('📸 Updated user profile with new photo:', userUpdateData);
        } catch (error) {
          console.error('Error updating user profile:', error);
          toast.warning('Photo uploaded but profile sync failed');
        }
      } else {
        throw new Error('Upload failed - no data returned');
      }
    } catch (error) {
      console.error(`Error uploading ${type} photo:`, error);
      toast.error(`Failed to upload ${type} photo`);
      
      // Fallback to local preview if upload fails
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (result) {
          if (type === 'digital') {
            setDigitalPhotoPreview(result);
          } else {
            setPrintPhotoPreview(result);
          }
          setFiles(prev => ({ ...prev, [type]: file }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent, type: 'digital' | 'print') => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(prev => ({ ...prev, [type]: true }));
    } else if (e.type === "dragleave") {
      setDragActive(prev => ({ ...prev, [type]: false }));
    }
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, type: 'digital' | 'print') => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(prev => ({ ...prev, [type]: false }));

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0], type);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'digital' | 'print') => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0], type);
    }
  };

  // Handle default photo checkbox
  const handleDefaultPhotoChange = (checked: boolean) => {
    setValue('useDefaultPhoto', checked);
    if (checked) {
      // Clear digital photo when using default
      setValue('photo', '');
      setDigitalPhotoPreview(null);
      setFiles(prev => ({ ...prev, digital: undefined }));
    }
  };

  return {
    // Base form functionality
    ...baseForm,

    // Photos-specific state
    digitalPhotoPreview,
    printPhotoPreview,
    dragActive,
    files,
    setDigitalPhotoPreview,
    setPrintPhotoPreview,
    setFiles,
    handleFileSelect,
    handleDrag,
    handleDrop,
    handleFileInputChange,
    handleDefaultPhotoChange,
  };
}