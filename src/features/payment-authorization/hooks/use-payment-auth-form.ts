"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { paymentAuthSchema, PaymentAuthFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";

export function usePaymentAuthForm() {
  const { userAuth } = useAuthContext();
  const { forms, updateForm, saveForm } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [files, setFiles] = React.useState<{ articlesOfIncorporation?: File }>({});
  const [fileSizeMessage, setFileSizeMessage] = React.useState({ isVisible: false, message: '' });

  const form = useForm<PaymentAuthFormData>({
    resolver: zodResolver(paymentAuthSchema) as any,
    defaultValues: {
      payrollRequired: true,
      payrollRequiredNo: false,
      brokerName: "",
      birthdate: "",
      sin: "",
      chequingAccount: false,
      savingsAccount: false,
      accountType: "",
      nameOnAccount: "",
      bankName: "",
      bankAddress: "",
      transitNumber: "",
      institutionNumber: "",
      accountNumber: "",
      companyAccount: false,
      businessNumber: "",
      articlesOfIncorporation: null,
      creditCardExpenses: false,
      signature: null,
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  // Load existing form data and populate with user data if firstSaveComplete is false
  React.useEffect(() => {
    if (userAuth.userInfo && forms.paymentAuthorization) {
      const formData = forms.paymentAuthorization;
      const user = userAuth.userInfo;

      // Check if this is the first time saving this form
      if (!formData.firstSaveComplete && !formData.userDataPopulated) {
        console.log('🔄 PAYMENT AUTH: First save not complete, populating with user data');

        // Map user data to form fields
        const userDataMappings = {
          brokerName: `${user.firstname || ''} ${user.lastname || ''}`.trim(),
          birthdate: user.birthdate || '',
          sin: user.sin || '',
        };

        // Set form values
        Object.entries(userDataMappings).forEach(([field, value]) => {
          if (value && typeof value === 'string' && value.trim() !== "") {
            setValue(field as keyof PaymentAuthFormData, value as any);
          }
        });

        // CRITICAL: Also update the forms context with the user data
        // This ensures that one-click signature can access the pre-populated data
        // Add a flag to prevent infinite loops
        updateForm('paymentAuthorization', {
          ...formData,
          ...userDataMappings,
          userDataPopulated: true, // Flag to prevent re-population
        });

        console.log('🔄 PAYMENT AUTH: Populated form and context with user data:', userDataMappings);
      } else {
        console.log('🔄 PAYMENT AUTH: First save complete or user data already populated, using saved form data');
      }

      // Define the form fields that should be loaded
      const formFields: (keyof PaymentAuthFormData)[] = [
        'payrollRequired', 'payrollRequiredNo', 'brokerName', 'birthdate', 'sin',
        'chequingAccount', 'savingsAccount', 'accountType', 'nameOnAccount',
        'bankName', 'bankAddress', 'transitNumber', 'institutionNumber',
        'accountNumber', 'companyAccount', 'businessNumber', 'articlesOfIncorporation',
        'creditCardExpenses', 'signature'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });
    }
  }, [userAuth.userInfo?.id, forms.paymentAuthorization?.firstSaveComplete, forms.paymentAuthorization?.userDataPopulated, setValue, updateForm]);

  // Handle account type selection (mutually exclusive)
  const handleAccountTypeChange = (type: 'chequing' | 'savings', checked: boolean) => {
    if (checked) {
      if (type === 'chequing') {
        setValue('chequingAccount', true);
        setValue('savingsAccount', false);
        setValue('accountType', 'Chequing');
      } else {
        setValue('chequingAccount', false);
        setValue('savingsAccount', true);
        setValue('accountType', 'Savings');
      }
    } else {
      setValue(type === 'chequing' ? 'chequingAccount' : 'savingsAccount', false);
      setValue('accountType', '');
    }
  };

  // Handle file selection
  const handleFileSelect = (file: File) => {
    setFiles({ ...files, articlesOfIncorporation: file });
  };

  // Handle file drop
  const handleFileDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (e.dataTransfer.items) {
      [...e.dataTransfer.items].forEach((item) => {
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            handleFileSelect(file);
          }
        }
      });
    }
  };

  // Handle payroll requirement change
  const handlePayrollRequiredChange = (required: boolean) => {
    setValue('payrollRequired', required);
    setValue('payrollRequiredNo', !required);
  };

  // Check if user is licensed
  const isLicensed = userAuth.userInfo?.license !== undefined && userAuth.userInfo?.license !== null;

  // Check if signature exists
  const hasSignature = watchedValues.signature && watchedValues.signature.url;

  // Check if form is complete based on payroll requirement
  const isFormComplete = watchedValues.payrollRequiredNo || 
    (watchedValues.payrollRequired && 
     watchedValues.brokerName && 
     watchedValues.bankName && 
     watchedValues.bankAddress && 
     watchedValues.transitNumber && 
     watchedValues.institutionNumber && 
     watchedValues.accountNumber && 
     watchedValues.nameOnAccount && 
     watchedValues.accountType &&
     (!watchedValues.companyAccount || (watchedValues.businessNumber && files.articlesOfIncorporation)) &&
     hasSignature);

  // Check if signature is required and saved (only if payroll is required)
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.paymentAuthorization;
    // If payroll is not required, no signature needed
    if (!watchedValues.payrollRequired) {
      return true;
    }
    // If firstSaveComplete is false, signature must be saved before form can be saved
    if (!formData?.firstSaveComplete) {
      return !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    }
    // If firstSaveComplete is true, form can be saved regardless of signature
    return true;
  }, [forms.paymentAuthorization, watchedValues.signature, watchedValues.payrollRequired]);

  const onSubmit = async (data: PaymentAuthFormData) => {
    setIsLoading(true);
    try {
      // Validate file size if uploaded
      if (files.articlesOfIncorporation) {
        const fileSize = Math.round(files.articlesOfIncorporation.size / 1024);
        if (fileSize >= 15 * 1024) {
          toast.error("The file size must be 15MB or less.");
          setIsLoading(false);
          return;
        }
      }

      // CRITICAL FIX: Preserve signature field from form context if it exists
      // This prevents overwriting the upload ID with a data URL
      const currentFormData = forms.paymentAuthorization || {};
      const preservedSignature = currentFormData.signature;

      const formDataToSubmit = {
        ...data,
        articlesOfIncorporation: files.articlesOfIncorporation,
        isFormComplete: isFormComplete,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      };

      // Extract signature ID properly - handle both number and object formats
      let signatureId = null;
      if (preservedSignature) {
        if (typeof preservedSignature === 'number') {
          signatureId = preservedSignature;
        } else if (typeof preservedSignature === 'object' && preservedSignature !== null && 'id' in preservedSignature) {
          signatureId = (preservedSignature as any).id;
        }
      }

      // If we have a signature ID, use it; otherwise use the form data signature
      if (signatureId) {
        formDataToSubmit.signature = signatureId;
      } else if (data.signature && typeof data.signature === 'number') {
        formDataToSubmit.signature = data.signature;
      }

      updateForm('paymentAuthorization', formDataToSubmit);

      await saveForm('paymentAuthorization');
      toast.success("Payment Authorization saved successfully!");
    } catch (error) {
      console.error("Error saving payment authorization:", error);
      toast.error("Failed to save payment authorization");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    files,
    fileSizeMessage,
    isLicensed,
    hasSignature,
    isFormComplete,
    isSignatureRequiredAndSaved,
    handleAccountTypeChange,
    handleFileSelect,
    handleFileDrop,
    handlePayrollRequiredChange,
    handleSubmit: handleSubmit(onSubmit),
  };
}
