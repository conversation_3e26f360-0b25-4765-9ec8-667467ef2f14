"use client";

import * as React from "react";
import { paymentAuthSchema, PaymentAuthFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";

export function usePaymentAuthForm() {
  const { userAuth } = useAuthContext();
  
  // Payment auth specific state
  const [files, setFiles] = React.useState<{ articlesOfIncorporation?: File }>({});
  const [fileSizeMessage, setFileSizeMessage] = React.useState({ isVisible: false, message: '' });

  // User field mappings for auto-population
  const userFieldMappings = {
    brokerName: 'firstname', // Could be combined firstname + lastname
    birthdate: 'birthdate',
    sin: 'sin',
  };

  // Transform function to handle broker name combination
  const transformSubmitData = React.useCallback((data: PaymentAuthFormData) => {
    // Combine first and last name for broker name if not already set
    if (!data.brokerName && userAuth.userInfo) {
      const firstName = userAuth.userInfo.firstname || '';
      const lastName = userAuth.userInfo.lastname || '';
      data.brokerName = `${firstName} ${lastName}`.trim();
    }
    
    return data;
  }, [userAuth.userInfo]);

  // Use the base form hook
  const baseForm = useBaseForm<PaymentAuthFormData>({
    formName: 'paymentAuthorization',
    schema: paymentAuthSchema,
    defaultValues: {
      payrollRequired: true,
      payrollRequiredNo: false,
      brokerName: "",
      birthdate: "",
      sin: "",
      chequingAccount: false,
      savingsAccount: false,
      accountType: "",
      nameOnAccount: "",
      bankName: "",
      bankAddress: "",
      transitNumber: "",
      institutionNumber: "",
      accountNumber: "",
      companyAccount: false,
      businessNumber: "",
      articlesOfIncorporation: null,
      creditCardExpenses: false,
      signature: null,
    },
    transformSubmitData,
  });

  const { form, watchedValues, errors } = baseForm;
  const { setValue } = form;

  // FIXED: Use enhanced data population hook to prevent infinite loops
  useFormDataPopulation('paymentAuthorization', form, {
    userFieldMappings,
    customPopulationLogic: React.useCallback((formData: any, userData: any, form: any) => {
      // Set combined broker name
      const firstName = userData.firstname || '';
      const lastName = userData.lastname || '';
      const fullName = `${firstName} ${lastName}`.trim();
      if (fullName) {
        setValue('brokerName', fullName);
      }
    }, [setValue]),
  });

  // Derived values
  const isLicensed = userAuth.userInfo?.licensed === true;
  const hasSignature = !!watchedValues.signature;
  const isFormComplete = !!(watchedValues.signature && watchedValues.nameOnAccount && watchedValues.bankName);
  
  // Check if signature is required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    // Check if signature is present and valid
    return !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
  }, [watchedValues.signature]);

  // Handler methods
  const handleAccountTypeChange = React.useCallback((accountType: 'checking' | 'savings') => {
    setValue('accountType', accountType);
    setValue('chequingAccount', accountType === 'checking');
    setValue('savingsAccount', accountType === 'savings');
  }, [setValue]);

  const handlePayrollRequiredChange = React.useCallback((required: boolean) => {
    setValue('payrollRequired', required);
    setValue('payrollRequiredNo', !required);
  }, [setValue]);

  const handleFileSelect = React.useCallback((file: File | null, fieldName: keyof typeof files) => {
    if (file) {
      const maxSize = 15 * 1024 * 1024; // 15MB
      if (file.size > maxSize) {
        setFileSizeMessage({
          isVisible: true,
          message: 'File size exceeds 15MB limit. Please choose a smaller file.'
        });
        return;
      }
      
      setFiles(prev => ({ ...prev, [fieldName]: file }));
      setFileSizeMessage({ isVisible: false, message: '' });
      
      // Set form field to file name or ID
      setValue(fieldName as keyof PaymentAuthFormData, file.name as any);
    } else {
      setFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[fieldName];
        return newFiles;
      });
      setValue(fieldName as keyof PaymentAuthFormData, null as any);
    }
  }, [setValue]);

  const handleFileDrop = React.useCallback((acceptedFiles: File[], fieldName: keyof typeof files) => {
    if (acceptedFiles.length > 0) {
      handleFileSelect(acceptedFiles[0], fieldName);
    }
  }, [handleFileSelect]);

  return {
    // Base form functionality
    ...baseForm,
    
    // Payment auth specific state
    files,
    fileSizeMessage,
    isLicensed,
    hasSignature,
    isFormComplete,
    isSignatureRequiredAndSaved,
    
    // Handler methods
    handleAccountTypeChange,
    handleFileSelect,
    handleFileDrop,
    handlePayrollRequiredChange,
  };
}
