"use client";

import * as React from "react";
import { letterOfDirectionSchema, LetterOfDirectionFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";

export function useLetterOfDirectionForm() {
  const { forms } = useFormsContext();
  const { userAuth } = useAuthContext();
  const [iframeWidth, setIframeWidth] = React.useState(1000);
  const [localMortgageSoftware, setLocalMortgageSoftware] = React.useState(
    (forms.brokerInfo as any)?.mortgageSoftware || ''
  );
  const [otherMtgSoftware, setOtherMtgSoftware] = React.useState(
    (forms.brokerInfo as any)?.mortgageSoftware === 'Other' || false
  );
  const [localOtherMortgageSoftware, setLocalOtherMortgageSoftware] = React.useState(
    (forms.brokerInfo as any)?.otherMortgageSoftware || ''
  );

  const user = userAuth.userInfo;

  // Mapping function to convert mortgageSoftware values to letterOfDirection property values
  const getMortgageSoftwareMapping = (mortgageSoftware: string) => {
    if (!mortgageSoftware || mortgageSoftware === 'Select') return 'expert';

    // Handle special cases
    if (mortgageSoftware === 'None') return null; // No PDF needed

    // Normalize the selected mortgage software to lowercase
    const normalizedSoftware = mortgageSoftware.toLowerCase();

    // Special exception: ExpertPro should use Expert letter of direction
    if (normalizedSoftware === 'expertpro') {
      return 'expert';
    }

    // For now, return the normalized software name
    // In a real implementation, you'd check against available documents
    return normalizedSoftware;
  };

  // Transform function to handle letter selection
  const transformSubmitData = React.useCallback((data: LetterOfDirectionFormData) => {
    // Determine the selected letter type based on mortgage software selection
    const selectedLetter = getMortgageSoftwareMapping(localMortgageSoftware || (forms.brokerInfo as any)?.mortgageSoftware || '');
    
    return {
      ...data,
      selectedLetter: selectedLetter,
      isFormComplete: data.acknowledgement === true,
    };
  }, [localMortgageSoftware, forms.brokerInfo]);

  // Use the base form hook
  const baseForm = useBaseForm<LetterOfDirectionFormData>({
    formName: 'letterOfDirection',
    schema: letterOfDirectionSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: () => ({
      mortgageSoftware: (forms.letterOfDirection as any)?.mortgageSoftware || (forms.brokerInfo as any)?.mortgageSoftware || "",
      otherMortgageSoftware: (forms.letterOfDirection as any)?.otherMortgageSoftware || (forms.brokerInfo as any)?.otherMortgageSoftware || "",
      selectedLetter: (forms.letterOfDirection as any)?.selectedLetter || "",
      acknowledgement: (forms.letterOfDirection as any)?.acknowledgement ?? false,
    }),
    transformSubmitData,
  });

  const { form, watchedValues } = baseForm;
  const { setValue } = form;

  // User field mappings - inherit mortgage software from broker info if available
  const userFieldMappings = {
    // Letter of direction might inherit mortgage software from user/broker info
    // Most fields are form-specific, so limited user data population
  };

  // Custom population logic to inherit data from broker info form
  const customPopulationLogic = React.useCallback((formData: any, userData: any, form: any) => {
    // Inherit mortgage software from broker info if available and not already set
    const brokerInfo = forms.brokerInfo;
    if (brokerInfo && !formData?.mortgageSoftware) {
      if (brokerInfo.mortgageSoftware) {
        setValue('mortgageSoftware', brokerInfo.mortgageSoftware);
        setLocalMortgageSoftware(brokerInfo.mortgageSoftware);
        setOtherMtgSoftware(brokerInfo.mortgageSoftware === 'Other');
        console.log('📄 Letter of Direction: Inherited mortgage software from broker info:', brokerInfo.mortgageSoftware);
      }
      
      if (brokerInfo.otherMortgageSoftware) {
        setValue('otherMortgageSoftware', brokerInfo.otherMortgageSoftware);
        setLocalOtherMortgageSoftware(brokerInfo.otherMortgageSoftware);
        console.log('📄 Letter of Direction: Inherited other mortgage software from broker info:', brokerInfo.otherMortgageSoftware);
      }
    }
  }, [setValue, forms.brokerInfo]);

  // Use enhanced data population with custom inheritance logic
  useFormDataPopulation('letterOfDirection', baseForm.form, {
    userFieldMappings,
    customPopulationLogic,
  });

  // Handle mortgage software selection changes
  const handleMortgageSoftwareChange = (value: string) => {
    setLocalMortgageSoftware(value);
    setValue('mortgageSoftware', value);

    if (value === 'Other') {
      setOtherMtgSoftware(true);
    } else {
      setOtherMtgSoftware(false);
      setLocalOtherMortgageSoftware('');
      setValue('otherMortgageSoftware', '');
    }
  };

  // Handle other mortgage software input changes
  const handleOtherMortgageSoftwareChange = (value: string) => {
    setLocalOtherMortgageSoftware(value);
    setValue('otherMortgageSoftware', value);
  };

  // Get PDF link based on mortgage software selection
  const getPdfLink = (software?: string) => {
    const mortgageSoftware = software || localMortgageSoftware || (forms.brokerInfo as any)?.mortgageSoftware;

    // Treat 'Select' as no selection, and return empty for 'None'
    if (!mortgageSoftware || mortgageSoftware === 'Select' || mortgageSoftware === 'None') {
      if (mortgageSoftware === 'None') {
        return '';
      }
      if (mortgageSoftware === 'Select') {
        return '';
      }
      // Default to expert if no value
      return '/documents/letter-of-direction-expert.pdf';
    }

    // For "Other" mortgage software, return empty
    if (mortgageSoftware === 'Other') {
      return '';
    }

    // Get the corresponding letterOfDirection value
    const letterOfDirectionValue = getMortgageSoftwareMapping(mortgageSoftware);

    // Return the PDF path based on the software
    return `/documents/letter-of-direction-${letterOfDirectionValue}.pdf`;
  };

  // Check if form is complete
  const isFormComplete = () => {
    return watchedValues.acknowledgement === true;
  };

  // Calculate iframe width based on screen size
  const calculateIframeWidth = () => {
    if (typeof window === 'undefined') {
      return 680;
    } else {
      const w = window.innerWidth;

      if (w > 0 && w < 540) {
        return 240;
      } else if (w >= 540 && w < 768) {
        return 420;
      } else if (w >= 768 && w < 992) {
        return 540;
      } else if (w >= 992 && w < 1280) {
        return 600;
      } else {
        return 860;
      }
    }
  };

  React.useEffect(() => {
    setIframeWidth(calculateIframeWidth());
  }, []);

  return {
    // Base form functionality
    ...baseForm,

    // Letter-specific state and handlers
    iframeWidth,
    localMortgageSoftware,
    otherMtgSoftware,
    localOtherMortgageSoftware,
    handleMortgageSoftwareChange,
    handleOtherMortgageSoftwareChange,
    getPdfLink,
    isFormComplete: isFormComplete(),
  };
}