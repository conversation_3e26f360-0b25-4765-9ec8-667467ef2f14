"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { contractSchema, ContractFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { toast } from "@/shared/lib/toast";

export function useContractForm() {
  const { forms, updateForm, saveForm } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<ContractFormData>({
    resolver: zodResolver(contractSchema) as any,
    defaultValues: {
      brokerSignature: forms.contractAndSchedule?.brokerSignature || "",
      brokerInitials: forms.contractAndSchedule?.brokerInitials || "",
      contractFileUrl: (forms.contractAndSchedule as any)?.contractFile?.url || "",
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  React.useEffect(() => {
    if (forms.contractAndSchedule) {
      const formData = forms.contractAndSchedule;

      // Check if this is the first time saving this form
      if (!formData.firstSaveComplete) {
        console.log('🔄 CONTRACT: First save not complete, using saved form data');
      } else {
        console.log('🔄 CONTRACT: First save complete, using saved form data');
      }

      // Define the form fields that should be loaded
      const formFields: (keyof ContractFormData)[] = [
        'brokerSignature', 'brokerInitials', 'contractFileUrl'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });

      // Handle contract file URL separately
      if ((formData as any).contractFile?.url) {
        setValue('contractFileUrl', (formData as any).contractFile.url);
      }
    }
  }, [forms.contractAndSchedule, setValue]);

  // Check if signatures are required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.contractAndSchedule;
    // If firstSaveComplete is false, signatures must be saved before form can be saved
    if (!formData?.firstSaveComplete) {
      const hasSignature = !!(watchedValues.brokerSignature && (typeof watchedValues.brokerSignature === 'number' || watchedValues.brokerSignature !== ''));
      const hasInitials = !!(watchedValues.brokerInitials && (typeof watchedValues.brokerInitials === 'number' || watchedValues.brokerInitials !== ''));
      return hasSignature && hasInitials;
    }
    // If firstSaveComplete is true, form can be saved regardless of signatures
    return true;
  }, [forms.contractAndSchedule, watchedValues.brokerSignature, watchedValues.brokerInitials]);

  const onSubmit = async (data: ContractFormData) => {
    setIsLoading(true);
    try {
      // CRITICAL FIX: Preserve signature fields from form context if they exist
      // This prevents overwriting the upload IDs with data URLs
      const currentFormData = forms.contractAndSchedule || {};
      const preservedBrokerSignature = currentFormData.brokerSignature;
      const preservedBrokerInitials = currentFormData.brokerInitials;

      const formDataToSubmit = {
        ...data,
        signatureDate: new Date().toISOString(),
        isFormComplete: true,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      };

      // Extract signature IDs properly - handle both number and object formats
      let brokerSignatureId = null;
      let brokerInitialsId = null;

      if (preservedBrokerSignature) {
        if (typeof preservedBrokerSignature === 'number') {
          brokerSignatureId = preservedBrokerSignature;
        } else if (typeof preservedBrokerSignature === 'object' && preservedBrokerSignature !== null && 'id' in preservedBrokerSignature) {
          brokerSignatureId = (preservedBrokerSignature as any).id;
        }
      }

      if (preservedBrokerInitials) {
        if (typeof preservedBrokerInitials === 'number') {
          brokerInitialsId = preservedBrokerInitials;
        } else if (typeof preservedBrokerInitials === 'object' && preservedBrokerInitials !== null && 'id' in preservedBrokerInitials) {
          brokerInitialsId = (preservedBrokerInitials as any).id;
        }
      }

      // If we have signature IDs, use them; otherwise use the form data signatures
      if (brokerSignatureId) {
        formDataToSubmit.brokerSignature = brokerSignatureId;
      } else if (data.brokerSignature && typeof data.brokerSignature === 'number') {
        formDataToSubmit.brokerSignature = data.brokerSignature;
      }

      if (brokerInitialsId) {
        formDataToSubmit.brokerInitials = brokerInitialsId;
      } else if (data.brokerInitials && typeof data.brokerInitials === 'number') {
        formDataToSubmit.brokerInitials = data.brokerInitials;
      }

      updateForm('contractAndSchedule', formDataToSubmit);

      await saveForm('contractAndSchedule');
      toast.success("Contract and Schedule saved successfully!");
    } catch (error) {
      console.error("Error saving contract:", error);
      toast.error("Failed to save contract");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    isSignatureRequiredAndSaved,
    handleSubmit: handleSubmit(onSubmit),
  };
}
