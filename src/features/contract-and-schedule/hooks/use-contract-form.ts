"use client";

import * as React from "react";
import { contractSchema, ContractFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useFormsContext } from "@/shared/contexts/forms-context";

export function useContractForm() {
  const { forms } = useFormsContext();

  // Use the base form hook
  const baseForm = useBaseForm<ContractFormData>({
    formName: 'contractAndSchedule',
    schema: contractSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: {
      brokerSignature: "",
      brokerInitials: "",
      contractFileUrl: "",
    },
  });

  const { form, watchedValues } = baseForm;

  // User field mappings for contract-related fields
  const userFieldMappings = {
    // Contract forms typically don't need extensive user data population
    // as they are more document-specific, but we'll add for consistency
  };

  // Use enhanced data population
  useFormDataPopulation('contractAndSchedule', baseForm.form, {
    userFieldMappings,
  });

  // FIXED: Correct signature flow logic
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.contractAndSchedule;
    const hasSignature = !!(watchedValues.brokerSignature && (typeof watchedValues.brokerSignature === 'number' || watchedValues.brokerSignature !== ''));
    const hasInitials = !!(watchedValues.brokerInitials && (typeof watchedValues.brokerInitials === 'number' || watchedValues.brokerInitials !== ''));
    
    // CRITICAL FIX: Check firstSaveComplete from context data, not watchedValues
    if (!formData?.firstSaveComplete) {
      // First save: require both signature and initials to be present
      return hasSignature && hasInitials;
    }
    
    // Subsequent saves: always allow submission (form has been saved before)
    return true;
  }, [forms.contractAndSchedule?.firstSaveComplete, watchedValues.brokerSignature, watchedValues.brokerInitials]);

  return {
    // Base form functionality
    ...baseForm,

    // Contract-specific state
    isSignatureRequiredAndSaved,
  };
}
