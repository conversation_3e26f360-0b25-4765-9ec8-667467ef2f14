"use client";

import * as React from "react";
import { unlicensedInfoSchema, UnlicensedInfoFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { useFormsContext } from "@/shared/contexts/forms-context";

export function useUnlicensedInfoForm() {
  const { userAuth } = useAuthContext();
  const { forms } = useFormsContext();
  const [charCount, setCharCount] = React.useState({ bio: 0, note: 0 });
  const [sameAddressWarning, setSameAddressWarning] = React.useState<{
    showMessage: boolean;
    sameAddress: boolean | null;
  }>({
    showMessage: false,
    sameAddress: null
  });
  const [branches, setBranches] = React.useState([]);

  // User field mappings for auto-population
  const userFieldMappings = {
    firstname: 'firstname',
    middlename: 'middlename',
    lastname: 'lastname',
    workEmail: 'workEmail',
    cellPhone: 'cellPhone',
    emergencyContact: 'emergencyContact',
    emergencyPhone: 'emergencyPhone',
  };

  // Use the base form hook
  const baseForm = useBaseForm<UnlicensedInfoFormData>({
    formName: 'unlicensedInfo',
    schema: unlicensedInfoSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: {
      firstname: "",
      middlename: "",
      lastname: "",
      workEmail: "",
      cellPhone: "",
      emergencyContact: "",
      emergencyPhone: "",
      assistantTo: "",
      completingCompliance: false,
      address: "",
      suiteUnit: "",
      city: "",
      province: "",
      postalCode: "",
      personalAddress: "",
      personalSuiteUnit: "",
      personalCity: "",
      personalProvince: "",
      personalPostalCode: "",
      signature: "",
    },
  });

  const { form, watchedValues } = baseForm;
  const { setValue } = form;

  // FIXED: Use enhanced data population hook to prevent infinite loops
  useFormDataPopulation('unlicensedInfo', form, {
    userFieldMappings,
  });

  // Load branches data
  React.useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (response.ok) {
          const data = await response.json();
          setBranches(data);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }
    };
    fetchBranches();
  }, []);

  // FIXED: Correct signature flow logic
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.unlicensedInfo;
    const hasSignature = !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    
    // CRITICAL FIX: Check firstSaveComplete from context data, not watchedValues
    if (!formData?.firstSaveComplete) {
      // First save: require signature to be present
      return hasSignature;
    }
    
    // Subsequent saves: always allow submission (form has been saved before)
    return true;
  }, [forms.unlicensedInfo?.firstSaveComplete, watchedValues.signature]);


  // Handle character counts
  const handleBioChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, bio: value.length }));
    // setValue('bio', value); // Field not in schema
  };

  const handleNoteChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, note: value.length }));
    // setValue('note', value); // Field not in schema
  };

  // Handle same address toggle
  const handleSameAddressChange = (checked: boolean) => {
    // setValue('sameAddress', checked); // Field not in schema
    if (checked) {
      setValue('personalAddress', watchedValues.address);
      setValue('personalCity', watchedValues.city);
      setValue('personalProvince', watchedValues.province);
      setValue('personalPostalCode', watchedValues.postalCode);
    }
  };

  // Handle address selection from branches
  const handleBranchSelect = (selectedAddress: any) => {
    setValue('address', selectedAddress.address || '');
    setValue('city', selectedAddress.city || '');
    setValue('province', selectedAddress.province || '');
    setValue('postalCode', selectedAddress.postalCode || '');
  };

  // Check if signature exists
  const hasSignature = () => {
    const signature = watchedValues.signature;
    if (!signature) return false;
    if (typeof signature === 'string') {
      return signature.length > 0;
    }
    if (typeof signature === 'object' && signature.url) {
      return true;
    }
    return false;
  };

  return {
    // Base form functionality
    ...baseForm,

    // Unlicensed info specific state and handlers
    charCount,
    sameAddressWarning,
    setSameAddressWarning,
    branches,
    hasSignature,
    isSignatureRequiredAndSaved,
    handleBioChange,
    handleNoteChange,
    handleSameAddressChange,
    handleBranchSelect,
  };
}
