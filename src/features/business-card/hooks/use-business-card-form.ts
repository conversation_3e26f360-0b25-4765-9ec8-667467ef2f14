"use client";

import * as React from "react";
import { businessCardSchema, BusinessCardFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";

export function useBusinessCardForm() {
  // Use the base form hook
  const baseForm = useBaseForm<BusinessCardFormData>({
    formName: 'businessCardInfo',
    schema: businessCardSchema,
    defaultValues: {
      businessCardOptOut: false,
    },
  });

  // Business card typically doesn't need extensive user data population,
  // but we'll add it for consistency and future extensibility
  const userFieldMappings = {
    // Add any relevant user field mappings if business card form gets user-related fields in the future
  };

  // Use enhanced data population
  useFormDataPopulation('businessCardInfo', baseForm.form, {
    userFieldMappings,
  });

  return {
    // Return all base form functionality
    ...baseForm,

    // Legacy compatibility - map to old property names
    onSubmit: baseForm.onSubmit,
    handleSubmit: baseForm.handleSubmit,
  };
}
