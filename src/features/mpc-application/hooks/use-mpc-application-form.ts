"use client";

import * as React from "react";
import { baseMpcApplicationSchema, MpcApplicationFormData, validateMpcApplication } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { toast } from "@/shared/lib/toast";
import { api } from "@/shared/lib/api";
import { getRawPhone } from "@/shared/lib/phone-utils";
import { formatUrl } from "@/shared/lib/url-utils";
import { serializeFormData, prepareForApiSubmission } from "@/shared/lib/form-serialization";
import { apiClient } from "@/shared/lib/api-client";
import { handleError, createAppError, ErrorType } from "@/shared/lib/error-handler";
import { getCookie } from "@/shared/lib/auth";

interface ValidationState {
  requiredFields: Array<{ id: string; label: string }>;
  crossFieldErrors: Array<{ id: string; label: string; message: string }>;
}

interface FormStatus {
  isComplete: boolean;
  completionPercentage: number;
  isLocked?: boolean;
}

export function useMpcApplicationForm() {
  const { userAuth } = useAuthContext();
  const { forms } = useFormsContext();
  
  // MPC-specific state
  const [branches, setBranches] = React.useState<any[]>([]);
  const [showDeclarationDetails, setShowDeclarationDetails] = React.useState(false);
  const [showJudgementUpload, setShowJudgementUpload] = React.useState(false);
  const [validationState, setValidationState] = React.useState<ValidationState>({
    requiredFields: [],
    crossFieldErrors: []
  });
  const [formStatus, setFormStatus] = React.useState<FormStatus>({
    isComplete: false,
    completionPercentage: 0
  });
  const [uploadedFiles, setUploadedFiles] = React.useState<Record<string, any>>({});
  const [processingStatus, setProcessingStatus] = React.useState<{
    visible: boolean;
    status: 'loading' | 'success' | 'error' | 'warning' | '';
    message: string;
  }>({
    visible: false,
    status: '',
    message: ''
  });

  // User field mappings for auto-population
  const userFieldMappings = {
    firstname: 'firstname',
    middlename: 'middlename',
    lastname: 'lastname',
    workEmail: 'workEmail',
    alternateEmail: 'email',
    position: 'position',
    address: 'address',
    city: 'city',
    province: 'province',
    postalCode: 'postalCode',
    workPhone: 'workPhone',
    cellPhone: 'cellPhone',
  };

  // Transform function for MPC-specific data processing
  const transformSubmitData = React.useCallback((data: MpcApplicationFormData) => {
    // Use enhanced serialization for consistent data processing
    const serializedData = serializeFormData(data, {
      includeEmpty: true,
      formatPhones: true,
      formatUrls: true,
      includeMetadata: false // We'll add our own metadata
    });

    // Add uploaded files
    const processedData = {
      ...serializedData,
      ...uploadedFiles
    };

    return processedData;
  }, [uploadedFiles]);

  // Success callback
  const onSuccess = React.useCallback((data: MpcApplicationFormData) => {
    console.log('MPC Application saved successfully:', data);
  }, []);

  // Error callback
  const onError = React.useCallback((error: Error) => {
    console.error('MPC Application save failed:', error);
  }, []);

  // Use the base form hook
  const baseForm = useBaseForm<MpcApplicationFormData>({
    formName: 'mpcApplication',
    schema: baseMpcApplicationSchema,
    mode: "onChange",
    transformSubmitData,
    onSuccess,
    onError,
  });

  const { form, watchedValues, errors } = baseForm;
  const { setValue, trigger } = form;

  // FIXED: Use enhanced data population hook to prevent infinite loops
  useFormDataPopulation('mpcApplication', form, {
    userFieldMappings,
  });

  // Load branches data
  React.useEffect(() => {
    const loadBranches = async () => {
      try {
        const response = await api.get('/branches');
        if (response.success && response.data) {
          setBranches(Array.isArray(response.data) ? response.data : []);
        }
      } catch (error) {
        console.error('Error loading branches:', error);
      }
    };
    loadBranches();
  }, []);

  // Update declaration details visibility
  React.useEffect(() => {
    const hasDeclarations = 
      watchedValues.declarationCriminalOffense ||
      watchedValues.declarationFraud ||
      watchedValues.declarationSuspended ||
      watchedValues.declarationLicenseDenied ||
      watchedValues.declarationBankruptcy;
    
    setShowDeclarationDetails(!!hasDeclarations);
  }, [
    watchedValues.declarationCriminalOffense,
    watchedValues.declarationFraud,
    watchedValues.declarationSuspended,
    watchedValues.declarationLicenseDenied,
    watchedValues.declarationBankruptcy
  ]);

  // Update judgement upload visibility
  React.useEffect(() => {
    setShowJudgementUpload(!!watchedValues.declarationBankruptcy);
  }, [watchedValues.declarationBankruptcy]);

  // Calculate form status
  React.useEffect(() => {
    const validation = validateMpcApplication(watchedValues);
    setFormStatus({
      isComplete: validation.isValid,
      completionPercentage: validation.isValid ? 100 : 0
    });
    
    setValidationState({
      requiredFields: validation.requiredFields || [],
      crossFieldErrors: []
    });
  }, [watchedValues]);

  // FIXED: Correct signature flow logic  
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.mpcApplication;
    const hasSignature = !!(watchedValues.applicantDeclarationSignature && 
      (typeof watchedValues.applicantDeclarationSignature === 'number' || 
       watchedValues.applicantDeclarationSignature !== ''));
    
    // CRITICAL FIX: Check firstSaveComplete from context data, not watchedValues
    if (!formData?.firstSaveComplete) {
      // First save: require signature to be present
      return hasSignature;
    }
    
    // Subsequent saves: always allow submission (form has been saved before)
    return true;
  }, [forms.mpcApplication?.firstSaveComplete, watchedValues.applicantDeclarationSignature]);

  // Handler methods
  const handleBranchSelect = React.useCallback((addressInfo: { address: string; city: string; province: string; postalCode: string }) => {
    setValue('officeAddress', addressInfo.address);
    setValue('officeSuiteUnit', ""); // AddressSelect doesn't provide suiteUnit
    setValue('officeCity', addressInfo.city);
    setValue('officeProvince', addressInfo.province);
    setValue('officePostalCode', addressInfo.postalCode);
    setValue('officeWebsite', ""); // AddressSelect doesn't provide website

    // Trigger validation
    setTimeout(() => {
      trigger(['officeAddress', 'officeCity', 'officeProvince', 'officePostalCode']);
    }, 100);
  }, [setValue, trigger]);

  const handleFileUpload = React.useCallback(async (file: File, fieldName: string) => {
    if (!file) return;

    baseForm.setProcessingStatus({
      visible: true,
      status: 'loading',
      message: 'Uploading file...'
    });

    try {
      // Set auth token if available
      const jwt = getCookie("jwt");
      if (jwt) {
        apiClient.setAuthToken(jwt);
      }

      // Use enhanced API client with progress tracking
      const response = await apiClient.uploadFile(
        '/upload',
        file,
        { field: fieldName },
        (progress) => {
          baseForm.setProcessingStatus({
            visible: true,
            status: 'loading',
            message: `Uploading file... ${progress}%`
          });
        }
      );

      if (response.success && response.data && response.data.length > 0) {
        const uploadedFile = response.data[0];
        
        // Update uploaded files state
        setUploadedFiles(prev => ({
          ...prev,
          [fieldName]: uploadedFile.id
        }));

        // Update form field
        setValue(fieldName as keyof MpcApplicationFormData, uploadedFile.id as any);

        baseForm.setProcessingStatus({
          visible: true,
          status: 'success',
          message: 'File uploaded successfully!'
        });

        toast.success('File uploaded successfully');

        // Hide success message after delay
        setTimeout(() => {
          baseForm.setProcessingStatus({ visible: false, status: '', message: '' });
        }, 2000);

      } else {
        throw new Error('Upload failed - no file data received');
      }

    } catch (error) {
      console.error('File upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'File upload failed';
      
      baseForm.setProcessingStatus({
        visible: true,
        status: 'error',
        message: errorMessage
      });

      toast.error(errorMessage);

      // Hide error message after delay
      setTimeout(() => {
        baseForm.setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);
    }
  }, [baseForm, setValue]);

  // Additional handler methods for compatibility
  const handleFormUpdate = React.useCallback(async () => {
    // This is now handled by the base form's handleSubmit
    const currentValues = form.getValues();
    await baseForm.onSubmit(currentValues);
  }, [form, baseForm]);

  return {
    // Base form functionality
    ...baseForm,

    // MPC-specific state
    branches,
    showDeclarationDetails,
    showJudgementUpload,
    formStatus,
    validationState,
    uploadedFiles,
    isSignatureRequiredAndSaved,
    processingStatus,

    // Handler methods
    handleBranchSelect,
    handleFileUpload,
    handleFormUpdate, // For compatibility with existing component
    setProcessingStatus,
  };
}
