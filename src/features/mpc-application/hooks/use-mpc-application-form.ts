"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { baseMpcApplicationSchema, MpcApplicationFormData, validateMpcApplication } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";
import { api } from "@/shared/lib/api";
import { getRawPhone } from "@/shared/lib/phone-utils";
import { formatUrl } from "@/shared/lib/url-utils";
import { checkSignature } from "@/shared/lib/signature-helper";
import { serializeFormData, prepareForApiSubmission } from "@/shared/lib/form-serialization";
import { apiClient } from "@/shared/lib/api-client";
import { handleError, createAppError, ErrorType } from "@/shared/lib/error-handler";
import { getCookie } from "@/shared/lib/auth";

interface ProcessingStatus {
  visible: boolean;
  status: 'loading' | 'success' | 'error' | '';
  message: string;
}

interface ValidationState {
  requiredFields: Array<{ id: string; label: string }>;
  crossFieldErrors: Array<{ id: string; label: string; message: string }>;
}

export function useMpcApplicationForm() {
  const router = useRouter();
  const { userAuth, setUserAuth } = useAuthContext();
  const { forms, updateForm, saveForm, onboardingId } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [branches, setBranches] = React.useState([]);
  const [showDeclarationDetails, setShowDeclarationDetails] = React.useState(false);
  const [showJudgementUpload, setShowJudgementUpload] = React.useState(false);
  const [processingStatus, setProcessingStatus] = React.useState<ProcessingStatus>({
    visible: false,
    status: '',
    message: ''
  });
  const [validationState, setValidationState] = React.useState<ValidationState>({
    requiredFields: [],
    crossFieldErrors: []
  });
  const [uploadedFiles, setUploadedFiles] = React.useState<Record<string, any>>({});
  const [formStatus, setFormStatus] = React.useState({
    isComplete: false,
    isLocked: false,
    completionPercentage: 0
  });

  const form = useForm<MpcApplicationFormData>({
    resolver: zodResolver(baseMpcApplicationSchema),
    mode: "onChange",
    defaultValues: {
      firstname: forms.mpcApplication?.firstname || "",
      middlename: forms.mpcApplication?.middlename || "",
      lastname: forms.mpcApplication?.lastname || "",
      preferredName: forms.mpcApplication?.preferredName || "",
      website: forms.mpcApplication?.website || "",
      workEmail: forms.mpcApplication?.workEmail || "",
      alternateEmail: forms.mpcApplication?.alternateEmail || "",
      position: forms.mpcApplication?.position || "",
      address: forms.mpcApplication?.address || "",
      suiteUnit: forms.mpcApplication?.suiteUnit || "",
      city: forms.mpcApplication?.city || "",
      province: forms.mpcApplication?.province || "",
      postalCode: forms.mpcApplication?.postalCode || "",
      workPhone: forms.mpcApplication?.workPhone || "",
      cellPhone: forms.mpcApplication?.cellPhone || "",
      tollfree: forms.mpcApplication?.tollfree || "",
      fax: forms.mpcApplication?.fax || "",
      officeAddress: forms.mpcApplication?.officeAddress || "",
      officeSuiteUnit: forms.mpcApplication?.officeSuiteUnit || "",
      officeCity: forms.mpcApplication?.officeCity || "",
      officeProvince: forms.mpcApplication?.officeProvince || "",
      officePostalCode: forms.mpcApplication?.officePostalCode || "",
      officeWebsite: forms.mpcApplication?.officeWebsite || "",
      declarationCriminalOffense: forms.mpcApplication?.declarationCriminalOffense === true ? "Yes" : forms.mpcApplication?.declarationCriminalOffense === false ? "No" : undefined,
      declarationFraud: forms.mpcApplication?.declarationFraud === true ? "Yes" : forms.mpcApplication?.declarationFraud === false ? "No" : undefined,
      declarationSuspended: forms.mpcApplication?.declarationSuspended === true ? "Yes" : forms.mpcApplication?.declarationSuspended === false ? "No" : undefined,
      declarationLicenseDenied: forms.mpcApplication?.declarationLicenseDenied === true ? "Yes" : forms.mpcApplication?.declarationLicenseDenied === false ? "No" : undefined,
      declarationBankruptcy: forms.mpcApplication?.declarationBankruptcy === true ? "Yes" : forms.mpcApplication?.declarationBankruptcy === false ? "No" : undefined,
      declarationDetails: forms.mpcApplication?.declarationDetails || "",
      judgementAction: forms.mpcApplication?.judgementAction || "",
      applicantDeclarationSignature: forms.mpcApplication?.applicantDeclarationSignature || "",
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  React.useEffect(() => {
    if (forms.mpcApplication) {
      const formData = forms.mpcApplication;

      // Define the form fields that should be loaded
      const formFields: (keyof MpcApplicationFormData)[] = [
        'firstname', 'middlename', 'lastname', 'preferredName', 'website', 'workEmail',
        'alternateEmail', 'position', 'address', 'suiteUnit', 'city', 'province',
        'postalCode', 'workPhone', 'cellPhone', 'tollfree', 'fax', 'officeAddress',
        'officeSuiteUnit', 'officeCity', 'officeProvince', 'officePostalCode',
        'applicantDeclarationSignature'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });
    }
  }, [forms.mpcApplication, setValue]);

  // CRITICAL FIX: Populate form with user data if firstSaveComplete is false
  React.useEffect(() => {
    if (userAuth.userInfo && forms.mpcApplication) {
      const formData = forms.mpcApplication;
      const user = userAuth.userInfo;

      // Check if this is the first time saving this form
      if (!formData.firstSaveComplete && !formData.userDataPopulated) {
        console.log('🔄 MPC APPLICATION: First save not complete, populating with user data');

        // Map user data to form fields
        const userDataMappings = {
          firstname: user.firstname || "",
          middlename: user.middlename || "",
          lastname: user.lastname || "",
          workEmail: user.workEmail || user.email || "",
          workPhone: user.workPhone || "",
          cellPhone: user.cellPhone || user.phone || "",
          position: user.position || "",
          website: user.website || "",
          province: user.province || "",
        };

        // Only set values that are not already set and have actual user data
        Object.entries(userDataMappings).forEach(([field, value]) => {
          if (value && typeof value === 'string' && value.trim() !== "") {
            setValue(field as keyof MpcApplicationFormData, value as any);
          }
        });

        // CRITICAL: Also update the forms context with the user data
        // This ensures that one-click signature can access the pre-populated data
        // Add a flag to prevent infinite loops
        updateForm('mpcApplication', {
          ...formData,
          ...userDataMappings,
          userDataPopulated: true, // Flag to prevent re-population
        });

        console.log('🔄 MPC APPLICATION: Populated form and context with user data:', userDataMappings);
      } else {
        console.log('🔄 MPC APPLICATION: First save complete or user data already populated, using saved form data');
      }
    }
  }, [userAuth.userInfo?.id, forms.mpcApplication?.firstSaveComplete, forms.mpcApplication?.userDataPopulated, setValue, updateForm]);

  // Fetch branches on component mount
  React.useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await apiClient.get('/branches?populate=*');

        if (response.success && response.data && Array.isArray(response.data)) {
          setBranches(response.data);
        } else if (response.success && response.data && response.data.data && Array.isArray(response.data.data)) {
          setBranches(response.data.data);
        } else {
          const appError = createAppError(
            'Failed to fetch branches',
            ErrorType.API,
            'BRANCHES_FETCH_FAILED',
            response,
            'MPC Application Form - Branch Loading'
          );
          await handleError(appError, { showToast: false, logToConsole: true });
          setBranches([]);
        }
      } catch (error) {
        const appError = createAppError(
          'Network error while fetching branches',
          ErrorType.NETWORK,
          'BRANCHES_NETWORK_ERROR',
          error,
          'MPC Application Form - Branch Loading'
        );
        await handleError(appError, { showToast: false, logToConsole: true });
        setBranches([]);
      }
    };

    fetchBranches();
  }, []);

  // Check if any declaration is Yes to show details textarea
  React.useEffect(() => {
    const hasYesDeclaration =
      watchedValues.declarationCriminalOffense === "Yes" ||
      watchedValues.declarationFraud === "Yes" ||
      watchedValues.declarationSuspended === "Yes" ||
      watchedValues.declarationLicenseDenied === "Yes" ||
      watchedValues.declarationBankruptcy === "Yes";

    setShowDeclarationDetails(hasYesDeclaration);

    // Show judgement upload if fraud declaration is Yes
    setShowJudgementUpload(watchedValues.declarationFraud === "Yes");
  }, [watchedValues.declarationCriminalOffense, watchedValues.declarationFraud, watchedValues.declarationSuspended, watchedValues.declarationLicenseDenied, watchedValues.declarationBankruptcy]);

  // Calculate completion percentage without running validation
  React.useEffect(() => {
    const calculateCompletionPercentage = () => {
      const requiredFields = [
        'firstname', 'lastname', 'workEmail', 'position', 'address', 'city',
        'province', 'postalCode', 'workPhone', 'officeAddress', 'officeCity',
        'officeProvince', 'officePostalCode', 'declarationCriminalOffense',
        'declarationFraud', 'declarationSuspended', 'declarationLicenseDenied',
        'declarationBankruptcy', 'applicantDeclarationSignature'
      ];

      // Add conditional required fields
      let conditionalRequiredCount = 0;
      if (showDeclarationDetails) {
        conditionalRequiredCount += 1; // declarationDetails
        if (showJudgementUpload) {
          conditionalRequiredCount += 1; // judgementAction
        }
      }

      const totalFields = requiredFields.length + conditionalRequiredCount;
      let completedFields = requiredFields.filter(field => {
        const value = watchedValues[field as keyof typeof watchedValues];
        return value !== undefined && value !== null && value !== '';
      }).length;

      // Add conditional fields if they exist
      if (showDeclarationDetails && watchedValues.declarationDetails) {
        completedFields += 1;
      }
      if (showJudgementUpload && watchedValues.judgementAction) {
        completedFields += 1;
      }

      const completionPercentage = Math.max(0, Math.min(95,
        Math.round((completedFields / totalFields) * 100)
      ));

      return completionPercentage;
    };

    const completionPercentage = calculateCompletionPercentage();
    
    setFormStatus(prev => {
      if (prev.completionPercentage !== completionPercentage ||
          prev.isLocked !== (userAuth?.forms?.isLocked || false)) {
        return {
          isComplete: completionPercentage === 100,
          isLocked: userAuth?.forms?.isLocked || false,
          completionPercentage
        };
      }
      return prev;
    });
  }, [watchedValues, showDeclarationDetails, showJudgementUpload, userAuth?.forms?.isLocked]);

  // Set last form visited
  React.useEffect(() => {
    if (setUserAuth) {
      setUserAuth(prev => ({
        ...prev,
        lastFormVisited: 'mpc-application'
      }));
    }
  }, [setUserAuth]);

  // Before leave handling
  React.useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (formStatus.isComplete || formStatus.isLocked) {
        return;
      }

      const hasUnsavedChanges = Object.keys(watchedValues).some(key => {
        const value = watchedValues[key as keyof typeof watchedValues];
        return value !== "" && value !== null && value !== undefined;
      });

      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [formStatus.isComplete, formStatus.isLocked]); // Removed watchedValues to prevent infinite loops

  // Auto-save functionality (debounced) - disabled to prevent infinite loops
  // React.useEffect(() => {
  //   const timeoutId = setTimeout(() => {
  //     if (!formStatus.isLocked && Object.keys(watchedValues).some(key => {
  //       const value = watchedValues[key as keyof typeof watchedValues];
  //       return value !== "" && value !== null && value !== undefined;
  //     })) {
  //       // Auto-save form data to context (not to server)
  //       const formData = form.getValues();
  //       const rawPhones = getRawPhone(formData);

  //       const processedData = {
  //         ...formData,
  //         ...rawPhones,
  //         website: formData.website ? formatUrl(formData.website) : '',
  //         officeWebsite: formData.officeWebsite ? formatUrl(formData.officeWebsite) : '',
  //         lastUpdated: new Date().toISOString(),
  //       };

  //       updateForm('mpcApplication', processedData);
  //     }
  //   }, 2000); // 2 second debounce

  //   return () => clearTimeout(timeoutId);
  // }, [watchedValues, formStatus.isLocked, form, updateForm]);

  // Handle branch selection
  const handleBranchSelect = (addressInfo: any) => {
    if (addressInfo) {
      setValue('officeAddress', addressInfo.address || '');
      setValue('officeCity', addressInfo.city || '');
      setValue('officeProvince', addressInfo.province || '');
      setValue('officePostalCode', addressInfo.postalCode || '');
    }
  };

  // Handle file upload with progress tracking
  const handleFileUpload = async (file: File): Promise<any> => {
    setProcessingStatus({
      visible: true,
      status: 'loading',
      message: 'Uploading file...'
    });

    try {
      // Set auth token if available
      const jwt = getCookie("jwt");
      if (jwt) {
        apiClient.setAuthToken(jwt);
      }

      // Use enhanced API client with progress tracking
      const response = await apiClient.uploadFile(
        '/upload',
        file,
        { field: 'judgementAction' },
        (progress) => {
          setProcessingStatus({
            visible: true,
            status: 'loading',
            message: `Uploading file... ${progress}%`
          });
        }
      );

      if (response.success && response.data && Array.isArray(response.data)) {
        const uploadedFile = response.data[0];
        setUploadedFiles(prev => ({ ...prev, judgementAction: uploadedFile }));
        setValue('judgementAction', uploadedFile);

        setProcessingStatus({
          visible: false,
          status: 'success',
          message: 'File uploaded successfully!'
        });

        return uploadedFile;
      } else {
        const appError = createAppError(
          response.error || 'Upload failed',
          ErrorType.FILE_UPLOAD,
          'FILE_UPLOAD_FAILED',
          response,
          'MPC Application Form - File Upload'
        );
        throw appError;
      }
    } catch (error) {
      const appError = error instanceof Error ?
        createAppError(
          error.message,
          ErrorType.FILE_UPLOAD,
          'FILE_UPLOAD_ERROR',
          error,
          'MPC Application Form - File Upload'
        ) :
        createAppError(
          'Unknown upload error',
          ErrorType.FILE_UPLOAD,
          'FILE_UPLOAD_UNKNOWN',
          error,
          'MPC Application Form - File Upload'
        );

      await handleError(appError, {
        showToast: true,
        logToConsole: true,
        fallbackMessage: 'File upload failed. Please try again.'
      });

      setProcessingStatus({
        visible: true,
        status: 'error',
        message: appError.message
      });
      throw appError;
    }
  };

  // Handle form update (save without validation)
  const handleFormUpdate = async () => {
    setProcessingStatus({
      visible: true,
      status: 'loading',
      message: 'Updating form...'
    });

    try {
      const formData = form.getValues();
      const validation = validateMpcApplication(formData);

      // CRITICAL FIX: Preserve signature field from form context if it exists
      // This prevents overwriting the upload ID with a data URL
      const currentFormData = forms.mpcApplication || {};
      const preservedSignature = currentFormData.applicantDeclarationSignature;

      // Use enhanced serialization for consistent data processing
      const serializedData = serializeFormData(formData, {
        includeEmpty: true,
        formatPhones: true,
        formatUrls: true,
        includeMetadata: false // We'll add our own metadata
      });

      const processedData = {
        ...serializedData,
        isFormComplete: validation.isValid,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
        ...uploadedFiles
      };

      // Extract signature ID properly - handle both number and object formats
      let signatureId = null;
      if (preservedSignature) {
        if (typeof preservedSignature === 'number') {
          signatureId = preservedSignature;
        } else if (typeof preservedSignature === 'object' && preservedSignature !== null && 'id' in preservedSignature) {
          signatureId = (preservedSignature as any).id;
        }
      }

      // If we have a signature ID, use it; otherwise use the form data signature
      if (signatureId) {
        processedData.applicantDeclarationSignature = signatureId;
      } else if (formData.applicantDeclarationSignature && typeof formData.applicantDeclarationSignature === 'number') {
        processedData.applicantDeclarationSignature = formData.applicantDeclarationSignature;
      }

      updateForm('mpcApplication', processedData);
      await saveForm('mpcApplication');

      setProcessingStatus({
        visible: false,
        status: 'success',
        message: 'Form updated successfully!'
      });

      // Check if form is complete and redirect if needed
      if (validation.isValid) {
        // Calculate overall completion percentage for all forms
        const allFormsComplete = Object.values(forms).every(form =>
          form && typeof form === 'object' && (form as any).isFormComplete
        );

        if (allFormsComplete) {
          router.push('/finished');
        }
      }
    } catch (error) {
      const appError = error instanceof Error ?
        createAppError(
          error.message,
          ErrorType.FORM_SUBMISSION,
          'FORM_UPDATE_ERROR',
          error,
          'MPC Application Form - Form Update'
        ) :
        createAppError(
          'Unknown form update error',
          ErrorType.FORM_SUBMISSION,
          'FORM_UPDATE_UNKNOWN',
          error,
          'MPC Application Form - Form Update'
        );

      await handleError(appError, {
        showToast: true,
        logToConsole: true,
        fallbackMessage: 'Failed to update form. Please try again.'
      });

      setProcessingStatus({
        visible: true,
        status: 'error',
        message: appError.message
      });
    }
  };

  // Handle form locking/unlocking
  const handleFormLock = async (shouldLock: boolean) => {
    try {
      setProcessingStatus({
        visible: true,
        status: 'loading',
        message: shouldLock ? 'Locking form...' : 'Unlocking form...'
      });

      // Update user auth context
      if (setUserAuth) {
        setUserAuth(prev => ({
          ...prev,
          forms: {
            ...prev.forms,
            isLocked: shouldLock
          }
        }));
      }

      setProcessingStatus({
        visible: false,
        status: 'success',
        message: shouldLock ? 'Form locked successfully' : 'Form unlocked successfully'
      });

    } catch (error) {
      setProcessingStatus({
        visible: true,
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to update form lock status'
      });
    }
  };

  // Handle form reset
  const handleFormReset = () => {
    form.reset();
    setProcessingStatus({ visible: false, status: '', message: '' });
    setValidationState({ requiredFields: [], crossFieldErrors: [] });
    setFormStatus({ isComplete: false, isLocked: false, completionPercentage: 0 });
  };

  // Handle form validation manually
  const handleFormValidation = async () => {
    const formData = form.getValues();
    const validation = validateMpcApplication(formData);

    // Convert validation to the format expected by the UI
    const requiredFields = validation.requiredFields.map(field => ({
      id: field,
      label: field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')
    }));

    setValidationState({
      requiredFields,
      crossFieldErrors: []
    });

    return validation;
  };

  // Handle signature validation
  const handleSignatureValidation = (signatureValue?: string) => {
    if (!signatureValue) {
      return { isSaved: false, isValid: false };
    }

    const signatureCheck = checkSignature({ url: signatureValue });
    return {
      isSaved: signatureCheck.isSaved,
      isValid: signatureCheck.isSaved && !!signatureCheck.data
    };
  };

  // Prepare form data for API submission
  const prepareFormForSubmission = () => {
    const formData = form.getValues();
    return prepareForApiSubmission(formData);
  };

  // Track form completion analytics
  const trackFormCompletion = async (completionData: {
    formName: string;
    completionPercentage: number;
    timeSpent?: number;
    fieldsCompleted?: string[];
  }) => {
    try {
      await apiClient.post('/analytics/form-completion', {
        ...completionData,
        userId: userAuth?.userInfo?.id,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // Analytics tracking is non-critical, just log the error
      const appError = createAppError(
        'Failed to track form completion',
        ErrorType.API,
        'ANALYTICS_TRACKING_FAILED',
        error,
        'MPC Application Form - Analytics'
      );
      await handleError(appError, { showToast: false, logToConsole: true });
    }
  };

  // Retry mechanism for failed operations
  const retryOperation = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context: string = 'Unknown Operation'
  ): Promise<T> => {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          const appError = createAppError(
            `Operation failed after ${maxRetries} attempts: ${lastError.message}`,
            ErrorType.API,
            'RETRY_EXHAUSTED',
            { originalError: lastError, attempts: maxRetries },
            context
          );
          await handleError(appError, {
            showToast: true,
            logToConsole: true,
            fallbackMessage: 'Operation failed. Please try again later.'
          });
          throw appError;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }

    throw lastError!;
  };

  // Check if signature is required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.mpcApplication;
    // If firstSaveComplete is false, signature must be saved before form can be saved
    if (!formData?.firstSaveComplete) {
      return !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    }
    // If firstSaveComplete is true, form can be saved regardless of signature
    return true;
  }, [forms.mpcApplication, watchedValues.signature]);

  const onSubmit = async (data: MpcApplicationFormData) => {
    await handleFormUpdate();
  };

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    branches,
    showDeclarationDetails,
    showJudgementUpload,
    processingStatus,
    formStatus,
    validationState,
    uploadedFiles,
    isSignatureRequiredAndSaved,
    handleBranchSelect,
    handleFileUpload,
    handleFormUpdate,
    handleFormLock,
    handleFormReset,
    handleFormValidation,
    handleSignatureValidation,
    prepareFormForSubmission,
    trackFormCompletion,
    retryOperation,
    setProcessingStatus,
    handleSubmit: handleSubmit(onSubmit),
  };
}