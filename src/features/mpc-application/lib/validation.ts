import * as z from "zod";

// URL validation helper
const urlSchema = z.string().optional().refine((val) => {
  if (!val) return true;
  try {
    new URL(val.startsWith('http') ? val : `https://${val}`);
    return true;
  } catch {
    return false;
  }
}, "Please enter a valid URL");

// Phone validation helper with enhanced validation
const phoneSchema = z.union([
  z.string(),
  z.object({
    masked: z.string(),
    raw: z.string()
  })
]).optional().refine((val) => {
  if (!val) return true;
  if (typeof val === 'string') {
    // Remove all non-digits and check length
    const digits = val.replace(/\D/g, '');
    return digits.length >= 10;
  }
  if (typeof val === 'object' && val.raw) {
    const digits = val.raw.replace(/\D/g, '');
    return digits.length >= 10;
  }
  return true;
}, "Phone number must be at least 10 digits");

// Required phone validation with enhanced rules
const requiredPhoneSchema = z.union([
  z.string().min(1, "Phone number is required"),
  z.object({
    masked: z.string().min(1, "Phone number is required"),
    raw: z.string().min(10, "Phone number must be at least 10 digits")
  })
]).refine((val) => {
  if (typeof val === 'string') {
    const digits = val.replace(/\D/g, '');
    return digits.length >= 10;
  }
  if (typeof val === 'object' && val.raw) {
    const digits = val.raw.replace(/\D/g, '');
    return digits.length >= 10;
  }
  return false;
}, "Phone number must be at least 10 digits");

// Enhanced postal code validation for Canadian postal codes
const postalCodeSchema = z.string().min(1, "Postal code is required").refine((val) => {
  // Canadian postal code format: A1A 1A1 or A1A1A1
  const canadianPostalRegex = /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/;
  return canadianPostalRegex.test(val.trim());
}, "Please enter a valid Canadian postal code (e.g., A1A 1A1)");

// Enhanced email validation with business email preference
const businessEmailSchema = z.string().min(1, "Email is required").email("Invalid email format").refine((val) => {
  // Warn about common personal email domains but don't reject
  const personalDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'live.com'];
  const domain = val.split('@')[1]?.toLowerCase();
  return !personalDomains.includes(domain);
}, "Consider using a business email address");

// Base schema without conditional validation
const baseMpcApplicationSchema = z.object({
  // Individual Information
  firstname: z.string().min(1, "First name is required"),
  middlename: z.string().optional(),
  lastname: z.string().min(1, "Last name is required"),
  preferredName: z.string().optional(),
  website: urlSchema,
  workEmail: businessEmailSchema,
  alternateEmail: z.string().email("Invalid email format").optional().or(z.literal("")),
  position: z.string().min(1, "Position is required"),

  // Mailing Address
  address: z.string().min(1, "Mailing address is required"),
  suiteUnit: z.string().optional(),
  city: z.string().min(1, "City is required"),
  province: z.string().min(1, "Province is required"),
  postalCode: postalCodeSchema,
  workPhone: requiredPhoneSchema,
  cellPhone: phoneSchema,
  tollfree: phoneSchema,
  fax: phoneSchema,

  // Office Location
  officeAddress: z.string().min(1, "Office address is required"),
  officeSuiteUnit: z.string().optional(),
  officeCity: z.string().min(1, "Office city is required"),
  officeProvince: z.string().min(1, "Office province is required"),
  officePostalCode: postalCodeSchema,
  officeWebsite: urlSchema,

  // Declarations - all required
  declarationCriminalOffense: z.enum(["Yes", "No"], {
    required_error: "Criminal offense declaration is required"
  }),
  declarationFraud: z.enum(["Yes", "No"], {
    required_error: "Fraud declaration is required"
  }),
  declarationSuspended: z.enum(["Yes", "No"], {
    required_error: "License suspension declaration is required"
  }),
  declarationLicenseDenied: z.enum(["Yes", "No"], {
    required_error: "License denial declaration is required"
  }),
  declarationBankruptcy: z.enum(["Yes", "No"], {
    required_error: "Bankruptcy declaration is required"
  }),

  // Conditional fields
  declarationDetails: z.string().optional(),
  judgementAction: z.any().optional(), // File upload - can be File or uploaded file object

  // Signature
  applicantDeclarationSignature: z.string().min(1, "Signature is required"),

  // Form metadata
  isFormComplete: z.boolean().optional(),
  firstSaveComplete: z.boolean().optional(),
  signatureDate: z.string().optional(),
  lastUpdated: z.string().optional(),
});

// Dynamic validation schema with conditional requirements
const mpcApplicationSchema = baseMpcApplicationSchema.refine((data) => {
  // Check if any declaration is "Yes"
  const hasYesDeclaration = [
    data.declarationCriminalOffense,
    data.declarationFraud,
    data.declarationSuspended,
    data.declarationLicenseDenied,
    data.declarationBankruptcy
  ].some(declaration => declaration === "Yes");

  // If any declaration is "Yes", declarationDetails is required
  if (hasYesDeclaration && (!data.declarationDetails || data.declarationDetails.trim() === "")) {
    return false;
  }

  return true;
}, {
  message: "Declaration details are required when any declaration is 'Yes'",
  path: ["declarationDetails"]
}).refine((data) => {
  // If fraud declaration is "Yes", judgement action file is required
  if (data.declarationFraud === "Yes" && !data.judgementAction) {
    return false;
  }

  return true;
}, {
  message: "Judgement action document is required when fraud declaration is 'Yes'",
  path: ["judgementAction"]
}).refine((data) => {
  // Cross-field validation: Ensure work email and alternate email are different
  if (data.workEmail && data.alternateEmail && data.workEmail === data.alternateEmail) {
    return false;
  }
  return true;
}, {
  message: "Work email and alternate email must be different",
  path: ["alternateEmail"]
}).refine((data) => {
  // Validate phone number uniqueness
  const phones = [data.workPhone, data.cellPhone, data.tollfree, data.fax].filter(Boolean);
  const phoneNumbers = phones.map(phone => {
    if (typeof phone === 'string') return phone.replace(/\D/g, '');
    if (typeof phone === 'object' && phone.raw) return phone.raw.replace(/\D/g, '');
    return '';
  }).filter(Boolean);

  const uniquePhones = new Set(phoneNumbers);
  return uniquePhones.size === phoneNumbers.length;
}, {
  message: "Phone numbers must be unique across all phone fields",
  path: ["cellPhone"]
});

export type MpcApplicationFormData = z.infer<typeof baseMpcApplicationSchema>;

// Validation helper functions
export function validateMpcApplication(data: any): {
  isValid: boolean;
  errors: Record<string, string>;
  requiredFields: Array<{ id: string; label: string }>;
} {
  try {
    mpcApplicationSchema.parse(data);
    return {
      isValid: true,
      errors: {},
      requiredFields: []
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      const requiredFields: Array<{ id: string; label: string }> = [];

      error.errors.forEach((err) => {
        const path = err.path.join('.');
        errors[path] = err.message;

        // Map field names to user-friendly labels
        const fieldLabels: Record<string, string> = {
          firstname: "First Name",
          lastname: "Last Name",
          workEmail: "Preferred Email Contact",
          position: "Position",
          address: "Mailing Address",
          city: "City",
          province: "Province",
          postalCode: "Postal Code",
          workPhone: "Preferred Phone Contact",
          officeAddress: "Office Address",
          officeCity: "Office City",
          officeProvince: "Office Province",
          officePostalCode: "Office Postal Code",
          declarationCriminalOffense: "Criminal Offense Declaration",
          declarationFraud: "Fraud Declaration",
          declarationSuspended: "License Suspension Declaration",
          declarationLicenseDenied: "License Denial Declaration",
          declarationBankruptcy: "Bankruptcy Declaration",
          declarationDetails: "Declaration Details",
          judgementAction: "Judgement Action Document",
          applicantDeclarationSignature: "Signature"
        };

        requiredFields.push({
          id: path,
          label: fieldLabels[path] || path
        });
      });

      return {
        isValid: false,
        errors,
        requiredFields
      };
    }

    return {
      isValid: false,
      errors: { general: "Validation failed" },
      requiredFields: []
    };
  }
}

// Field-specific validation function
export function validateField(fieldName: string, value: any, allData?: any): {
  isValid: boolean;
  error?: string;
} {
  try {
    // Get the field schema from the base schema (before refine methods)
    const fieldSchema = baseMpcApplicationSchema.shape[fieldName as keyof typeof baseMpcApplicationSchema.shape];

    if (!fieldSchema) {
      return { isValid: true }; // Field not in schema, assume valid
    }

    // Special handling for conditional fields
    if (fieldName === 'declarationDetails' && allData) {
      const hasYesDeclaration =
        allData.declarationCriminalOffense === "Yes" ||
        allData.declarationFraud === "Yes" ||
        allData.declarationSuspended === "Yes" ||
        allData.declarationLicenseDenied === "Yes" ||
        allData.declarationBankruptcy === "Yes";

      if (!hasYesDeclaration) {
        return { isValid: true }; // Not required if no Yes declarations
      }
    }

    if (fieldName === 'judgementAction' && allData) {
      if (allData.declarationFraud !== "Yes") {
        return { isValid: true }; // Not required if fraud declaration is not Yes
      }
    }

    // Validate the field
    fieldSchema.parse(value);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.errors[0]?.message || 'Invalid value'
      };
    }
    return {
      isValid: false,
      error: 'Validation failed'
    };
  }
}

// Get validation requirements for a field
export function getFieldRequirements(fieldName: string): {
  required: boolean;
  type: string;
  constraints?: string[];
} {
  const fieldSchema = baseMpcApplicationSchema.shape[fieldName as keyof typeof baseMpcApplicationSchema.shape];

  if (!fieldSchema) {
    return { required: false, type: 'unknown' };
  }

  const constraints: string[] = [];

  // Analyze schema to extract requirements
  if (fieldSchema._def.typeName === 'ZodString') {
    const stringSchema = fieldSchema as z.ZodString;
    if (stringSchema._def.checks) {
      stringSchema._def.checks.forEach(check => {
        if (check.kind === 'min') {
          constraints.push(`Minimum ${check.value} characters`);
        }
        if (check.kind === 'max') {
          constraints.push(`Maximum ${check.value} characters`);
        }
        if (check.kind === 'email') {
          constraints.push('Must be a valid email address');
        }
      });
    }
  }

  return {
    required: !fieldSchema.isOptional(),
    type: fieldSchema._def.typeName.replace('Zod', '').toLowerCase(),
    constraints: constraints.length > 0 ? constraints : undefined
  };
}

export { mpcApplicationSchema, baseMpcApplicationSchema };