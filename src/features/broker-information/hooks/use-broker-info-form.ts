"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { brokerInfoSchema, BrokerInfoFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";
import { formatUrl } from "@/shared/lib/format-url";

import { getCookie } from "@/shared/lib/auth";

export function useBrokerInfoForm() {
  const { userAuth, updateUserData } = useAuthContext();
  const { forms, updateForm, saveForm } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [charCount, setCharCount] = React.useState({ bio: 0, additionalNotes: 0 });
  const [sameAddressWarning, setSameAddressWarning] = React.useState({
    showMessage: false,
    sameAddress: null as boolean | null,
  });
  const [branches, setBranches] = React.useState<any[]>([]);
  const [showOtherMortgageSoftware, setShowOtherMortgageSoftware] = React.useState(false);
  const [processingStatus, setProcessingStatus] = React.useState({
    visible: false,
    status: '',
    message: ''
  });
  const [formStatus, setFormStatus] = React.useState({
    isComplete: false,
    completionPercentage: 0
  });
  const [validationErrors, setValidationErrors] = React.useState<{
    fieldErrors: Record<string, string[]>;
    crossFieldErrors: string[];
    requiredFields: string[];
  }>({
    fieldErrors: {},
    crossFieldErrors: [],
    requiredFields: []
  });

  // Create default values that will be updated when forms context loads
  const getDefaultValues = React.useCallback((): BrokerInfoFormData => {
    const formData = forms.brokerInfo;
    console.log('🔄 BROKER INFO FORM - getDefaultValues called, formData:', formData);
    console.log('🔄 BROKER INFO FORM - getDefaultValues formData.signature:', formData?.signature);

    if (formData) {
      return {
        // Personal Information
        firstName: formData.firstName || "",
        middleName: formData.middleName || "",
        lastName: formData.lastName || "",
        legalName: formData.legalName || "",
        preferredName: formData.preferredName || "",
        titles: formData.titles || "",
        position: formData.position || "",
        license: formData.license || "",
        birthdate: formData.birthdate || "",
        sin: formData.sin || "",
        tshirtSize: formData.tshirtSize || "M",
        bio: formData.bio || "",
        additionalNotes: formData.additionalNotes || "",

        // Contact Information
        workEmail: formData.workEmail || "",
        workPhone: formData.workPhone || "",
        ext: formData.ext || "",
        homePhone: formData.homePhone || "",
        cellPhone: formData.cellPhone || "",
        emergencyContact: formData.emergencyContact || "",
        emergencyPhone: formData.emergencyPhone || "",

        // Office Address
        address: formData.address || "",
        suiteUnit: formData.suiteUnit || "",
        city: formData.city || "",
        province: formData.province || "",
        postalCode: formData.postalCode || "",
        brokerageLicense: formData.brokerageLicense || "",

        // Personal Address
        personalAddress: formData.personalAddress || "",
        personalSuiteUnit: formData.personalSuiteUnit || "",
        personalCity: formData.personalCity || "",
        personalProvince: formData.personalProvince || "",
        personalPostalCode: formData.personalPostalCode || "",
        sameAddress: formData.sameAddress || false,

        // Brokering Background
        existingAgent: formData.existingAgent,
        mortgageSoftware: formData.mortgageSoftware || "",
        otherMortgageSoftware: formData.otherMortgageSoftware || "",
        lender1: formData.lender1 || "",
        lender1Volume: formData.lender1Volume || "",
        lender2: formData.lender2 || "",
        lender2Volume: formData.lender2Volume || "",
        lender3: formData.lender3 || "",
        lender3Volume: formData.lender3Volume || "",

        // Enhanced Social Media
        hasFacebook: formData.hasFacebook || false,
        facebook: formData.facebook || "",
        facebookHandler: formData.facebookHandler || "",
        hasInstagram: formData.hasInstagram || false,
        instagram: formData.instagram || "",
        instagramHandler: formData.instagramHandler || "",
        hasLinkedin: formData.hasLinkedin || false,
        linkedin: formData.linkedin || "",
        linkedinHandler: formData.linkedinHandler || "",
        hasYoutube: formData.hasYoutube || false,
        youtube: formData.youtube || "",
        youtubeHandler: formData.youtubeHandler || "",
        hasTwitter: formData.hasTwitter || false,
        twitter: formData.twitter || "",
        twitterHandler: formData.twitterHandler || "",
        hasTikTok: formData.hasTikTok || false,
        tiktok: formData.tiktok || "",
        tiktokHandler: formData.tiktokHandler || "",
        hasPinterest: formData.hasPinterest || false,
        pinterest: formData.pinterest || "",
        pinterestHandler: formData.pinterestHandler || "",
        hasThreads: formData.hasThreads || false,
        threads: formData.threads || "",
        threadsHandler: formData.threadsHandler || "",
        hasBluesky: formData.hasBluesky || false,
        bluesky: formData.bluesky || "",
        blueskyHandler: formData.blueskyHandler || "",

        // Declarations
        declarationRegulatoryReview: formData.declarationRegulatoryReview,
        declarationClientComplaints: formData.declarationClientComplaints,
        declarationRegulatoryReviewDetails: formData.declarationRegulatoryReviewDetails || "",
        declarationClientComplaintsDetails: formData.declarationClientComplaintsDetails || "",

        // Signature - CRITICAL: Ensure this is properly set
        signature: (() => {
          const sig = formData.signature || "";
          console.log('🔄 BROKER INFO FORM - Setting signature in default values to:', sig, 'type:', typeof sig);
          return sig;
        })(),

        // Form metadata
        isFormComplete: formData.isFormComplete || false,
        firstSaveComplete: formData.firstSaveComplete || false,
      };
    }

    // Default values when no form data exists
    return {
      // Personal Information
      firstName: "",
      middleName: "",
      lastName: "",
      legalName: "",
      preferredName: "",
      titles: "",
      position: "",
      license: "",
      birthdate: "",
      sin: "",
      tshirtSize: "M",
      bio: "",
      additionalNotes: "",

      // Contact Information
      workEmail: "",
      workPhone: "",
      ext: "",
      homePhone: "",
      cellPhone: "",
      emergencyContact: "",
      emergencyPhone: "",

      // Office Address
      address: "",
      suiteUnit: "",
      city: "",
      province: "",
      postalCode: "",
      brokerageLicense: "",

      // Personal Address
      personalAddress: "",
      personalSuiteUnit: "",
      personalCity: "",
      personalProvince: "",
      personalPostalCode: "",
      sameAddress: false,

      // Brokering Background
      existingAgent: undefined,
      mortgageSoftware: "",
      otherMortgageSoftware: "",
      lender1: "",
      lender1Volume: "",
      lender2: "",
      lender2Volume: "",
      lender3: "",
      lender3Volume: "",

      // Enhanced Social Media
      hasFacebook: false,
      facebook: "",
      facebookHandler: "",
      hasInstagram: false,
      instagram: "",
      instagramHandler: "",
      hasLinkedin: false,
      linkedin: "",
      linkedinHandler: "",
      hasYoutube: false,
      youtube: "",
      youtubeHandler: "",
      hasTwitter: false,
      twitter: "",
      twitterHandler: "",
      hasTikTok: false,
      tiktok: "",
      tiktokHandler: "",
      hasPinterest: false,
      pinterest: "",
      pinterestHandler: "",
      hasThreads: false,
      threads: "",
      threadsHandler: "",
      hasBluesky: false,
      bluesky: "",
      blueskyHandler: "",

      // Declarations
      declarationRegulatoryReview: undefined,
      declarationClientComplaints: undefined,
      declarationRegulatoryReviewDetails: "",
      declarationClientComplaintsDetails: "",

      // Signature
      signature: "",

      // Form metadata
      isFormComplete: false,
      firstSaveComplete: false,
    };
  }, [forms.brokerInfo]);

  const form = useForm<BrokerInfoFormData>({
    resolver: zodResolver(brokerInfoSchema) as any,
    defaultValues: getDefaultValues(),
  });

  const { watch, setValue, handleSubmit, reset, formState: { errors } } = form;
  const watchedValues = watch();

  // Debug watched values
  React.useEffect(() => {
    console.log('🔄 BROKER INFO FORM - Current watched values signature:', watchedValues.signature);
    console.log('🔄 BROKER INFO FORM - Current watched values signature type:', typeof watchedValues.signature);
  }, [watchedValues.signature]);

  // CRITICAL FIX: Reset form when forms context data changes
  React.useEffect(() => {
    console.log('🔄 BROKER INFO FORM - Forms context changed, resetting form with new data');
    console.log('🔄 BROKER INFO FORM - forms.brokerInfo exists:', !!forms.brokerInfo);

    if (forms.brokerInfo) {
      console.log('🔄 BROKER INFO FORM - forms.brokerInfo.signature:', forms.brokerInfo.signature);

      // Create new default values directly here to avoid callback issues
      const formData = forms.brokerInfo;
      const newDefaultValues: BrokerInfoFormData = {
        // Personal Information
        firstName: formData.firstName || "",
        middleName: formData.middleName || "",
        lastName: formData.lastName || "",
        legalName: formData.legalName || "",
        preferredName: formData.preferredName || "",
        titles: formData.titles || "",
        position: formData.position || "",
        license: formData.license || "",
        birthdate: formData.birthdate || "",
        sin: formData.sin || "",
        tshirtSize: formData.tshirtSize || "M",
        bio: formData.bio || "",
        additionalNotes: formData.additionalNotes || "",

        // Contact Information
        workEmail: formData.workEmail || "",
        workPhone: formData.workPhone || "",
        ext: formData.ext || "",
        homePhone: formData.homePhone || "",
        cellPhone: formData.cellPhone || "",
        emergencyContact: formData.emergencyContact || "",
        emergencyPhone: formData.emergencyPhone || "",

        // Office Address
        address: formData.address || "",
        suiteUnit: formData.suiteUnit || "",
        city: formData.city || "",
        province: formData.province || "",
        postalCode: formData.postalCode || "",
        brokerageLicense: formData.brokerageLicense || "",

        // Personal Address
        personalAddress: formData.personalAddress || "",
        personalSuiteUnit: formData.personalSuiteUnit || "",
        personalCity: formData.personalCity || "",
        personalProvince: formData.personalProvince || "",
        personalPostalCode: formData.personalPostalCode || "",
        sameAddress: formData.sameAddress || false,

        // Brokering Background
        existingAgent: formData.existingAgent,
        mortgageSoftware: formData.mortgageSoftware || "",
        otherMortgageSoftware: formData.otherMortgageSoftware || "",
        lender1: formData.lender1 || "",
        lender1Volume: formData.lender1Volume || "",
        lender2: formData.lender2 || "",
        lender2Volume: formData.lender2Volume || "",
        lender3: formData.lender3 || "",
        lender3Volume: formData.lender3Volume || "",

        // Enhanced Social Media
        hasFacebook: formData.hasFacebook || false,
        facebook: formData.facebook || "",
        facebookHandler: formData.facebookHandler || "",
        hasInstagram: formData.hasInstagram || false,
        instagram: formData.instagram || "",
        instagramHandler: formData.instagramHandler || "",
        hasLinkedin: formData.hasLinkedin || false,
        linkedin: formData.linkedin || "",
        linkedinHandler: formData.linkedinHandler || "",
        hasYoutube: formData.hasYoutube || false,
        youtube: formData.youtube || "",
        youtubeHandler: formData.youtubeHandler || "",
        hasTwitter: formData.hasTwitter || false,
        twitter: formData.twitter || "",
        twitterHandler: formData.twitterHandler || "",
        hasTikTok: formData.hasTikTok || false,
        tiktok: formData.tiktok || "",
        tiktokHandler: formData.tiktokHandler || "",
        hasPinterest: formData.hasPinterest || false,
        pinterest: formData.pinterest || "",
        pinterestHandler: formData.pinterestHandler || "",
        hasThreads: formData.hasThreads || false,
        threads: formData.threads || "",
        threadsHandler: formData.threadsHandler || "",
        hasBluesky: formData.hasBluesky || false,
        bluesky: formData.bluesky || "",
        blueskyHandler: formData.blueskyHandler || "",

        // Declarations
        declarationRegulatoryReview: formData.declarationRegulatoryReview,
        declarationClientComplaints: formData.declarationClientComplaints,
        declarationRegulatoryReviewDetails: formData.declarationRegulatoryReviewDetails || "",
        declarationClientComplaintsDetails: formData.declarationClientComplaintsDetails || "",

        // Signature - CRITICAL: Ensure this is properly set
        signature: formData.signature || "",

        // Form metadata
        isFormComplete: formData.isFormComplete || false,
        firstSaveComplete: formData.firstSaveComplete || false,
      };

      console.log('🔄 BROKER INFO FORM - New default values signature:', newDefaultValues.signature);
      console.log('🔄 BROKER INFO FORM - New default values signature type:', typeof newDefaultValues.signature);
      reset(newDefaultValues);
    }
  }, [forms.brokerInfo, reset]);

  // Set additional form state when form data changes
  React.useEffect(() => {
    if (forms.brokerInfo) {
      const formData = forms.brokerInfo;

      // Set character counts
      if (formData.bio) {
        setCharCount(prev => ({ ...prev, bio: formData.bio?.length || 0 }));
      }
      if (formData.additionalNotes) {
        setCharCount(prev => ({ ...prev, additionalNotes: formData.additionalNotes?.length || 0 }));
      }

      // Set mortgage software conditional state
      if (formData.mortgageSoftware === "Other") {
        setShowOtherMortgageSoftware(true);
      }

      // Update form status
      setFormStatus({
        isComplete: formData.isFormComplete || false,
        completionPercentage: calculateCompletionPercentage(formData)
      });
    }
  }, [forms.brokerInfo]);

  // CRITICAL FIX: Populate form with user data if firstSaveComplete is false
  React.useEffect(() => {
    if (userAuth.userInfo && forms.brokerInfo) {
      const formData = forms.brokerInfo;
      const user = userAuth.userInfo;

      // Check if this is the first time saving this form
      if (!formData.firstSaveComplete && !formData.userDataPopulated) {
        console.log('🔄 BROKER INFO: First save not complete, populating with user data');

        // Map user data to form fields
        const userDataMappings = {
          firstName: user.firstname || "",
          middleName: user.middlename || "",
          lastName: user.lastname || "",
          workEmail: user.workEmail || user.email || "",
          workPhone: user.workPhone || "",
          cellPhone: user.cellPhone || user.phone || "",
          position: user.position || "",
          license: user.license || "",
          bio: user.bio || "",
          brokerage: user.brokerage || "",
          province: user.province || "",
          // Social media
          facebook: user.facebook || "",
          instagram: user.instagram || "",
          linkedin: user.linkedin || "",
          twitter: user.twitter || "",
          youtube: user.youtube || "",
        };

        // Only set values that are not already set and have actual user data
        Object.entries(userDataMappings).forEach(([field, value]) => {
          if (value && typeof value === 'string' && value.trim() !== "") {
            setValue(field as keyof BrokerInfoFormData, value as any);
          }
        });

        // CRITICAL: Also update the forms context with the user data
        // This ensures that one-click signature can access the pre-populated data
        // Add a flag to prevent infinite loops
        updateForm('brokerInfo', {
          ...formData,
          ...userDataMappings,
          userDataPopulated: true, // Flag to prevent re-population
        });

        console.log('🔄 BROKER INFO: Populated form and context with user data:', userDataMappings);
      } else {
        console.log('🔄 BROKER INFO: First save complete or user data already populated, using saved form data');
      }
    }
  }, [userAuth.userInfo?.id, forms.brokerInfo?.firstSaveComplete, forms.brokerInfo?.userDataPopulated, setValue, updateForm]);

  // Calculate form completion percentage
  const calculateCompletionPercentage = (data: any): number => {
    const requiredFields = [
      'firstName', 'lastName', 'position', 'workEmail', 'address', 'city', 'province', 'postalCode',
      'personalAddress', 'personalCity', 'personalProvince', 'personalPostalCode',
      'workPhone', 'birthdate', 'sin', 'emergencyContact', 'emergencyPhone', 'bio'
    ];

    // Add Saskatchewan-specific field
    if (data.province === "Saskatchewan") {
      requiredFields.push('brokerageLicense');
    }

    // Add declaration fields for licensed users
    if (isLicensed) {
      requiredFields.push('declarationRegulatoryReview', 'declarationClientComplaints');
      if (data.declarationRegulatoryReview === true) {
        requiredFields.push('declarationRegulatoryReviewDetails');
      }
      if (data.declarationClientComplaints === true) {
        requiredFields.push('declarationClientComplaintsDetails');
      }
    }

    const completedFields = requiredFields.filter(field => {
      const value = data[field];
      return value !== undefined && value !== null && value !== '';
    });

    return Math.round((completedFields.length / requiredFields.length) * 100);
  };

  // Validate form and update validation errors
  const validateForm = (data: any) => {
    const fieldErrors: Record<string, string[]> = {};
    const crossFieldErrors: string[] = [];
    const requiredFields: string[] = [];

    // Required field validation
    const requiredFieldsList = [
      { field: 'firstName', label: 'First Name' },
      { field: 'lastName', label: 'Last Name' },
      { field: 'position', label: 'Position' },
      { field: 'workEmail', label: 'Work Email' },
      { field: 'address', label: 'Office Address' },
      { field: 'city', label: 'City' },
      { field: 'province', label: 'Province' },
      { field: 'postalCode', label: 'Postal Code' },
      { field: 'personalAddress', label: 'Personal Address' },
      { field: 'personalCity', label: 'Personal City' },
      { field: 'personalProvince', label: 'Personal Province' },
      { field: 'personalPostalCode', label: 'Personal Postal Code' },
      { field: 'workPhone', label: 'Work Phone' },
      { field: 'birthdate', label: 'Birth Date' },
      { field: 'sin', label: 'SIN' },
      { field: 'emergencyContact', label: 'Emergency Contact' },
      { field: 'emergencyPhone', label: 'Emergency Phone' },
      { field: 'bio', label: 'Bio' }
    ];

    // Add Saskatchewan-specific field
    if (data.province === "Saskatchewan") {
      requiredFieldsList.push({ field: 'brokerageLicense', label: 'Brokerage License' });
    }

    // Add declaration fields for licensed users
    if (isLicensed) {
      requiredFieldsList.push(
        { field: 'declarationRegulatoryReview', label: 'Regulatory Review Declaration' },
        { field: 'declarationClientComplaints', label: 'Client Complaints Declaration' }
      );

      if (data.declarationRegulatoryReview === true) {
        requiredFieldsList.push({ field: 'declarationRegulatoryReviewDetails', label: 'Regulatory Review Details' });
      }
      if (data.declarationClientComplaints === true) {
        requiredFieldsList.push({ field: 'declarationClientComplaintsDetails', label: 'Client Complaints Details' });
      }
    }

    // Check required fields
    requiredFieldsList.forEach(({ field, label }) => {
      const value = data[field];
      if (value === undefined || value === null || value === '') {
        if (!fieldErrors[field]) fieldErrors[field] = [];
        fieldErrors[field].push(`${label} is required`);
        requiredFields.push(label);
      }
    });

    // Cross-field validation
    if (data.mortgageSoftware === "Other" && (!data.otherMortgageSoftware || data.otherMortgageSoftware.trim() === '')) {
      crossFieldErrors.push('Please specify the other mortgage software when "Other" is selected');
    }

    // Email validation
    if (data.workEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.workEmail)) {
      if (!fieldErrors.workEmail) fieldErrors.workEmail = [];
      fieldErrors.workEmail.push('Please enter a valid email address');
    }

    // Phone validation
    const phoneFields = ['workPhone', 'homePhone', 'cellPhone', 'emergencyPhone'];
    phoneFields.forEach(field => {
      const value = data[field];
      if (value && !/^\(\d{3}\) \d{3}-\d{4}$/.test(value)) {
        if (!fieldErrors[field]) fieldErrors[field] = [];
        fieldErrors[field].push('Please enter a valid phone number format: (*************');
      }
    });

    // SIN validation
    if (data.sin && !/^\d{3}-\d{3}-\d{3}$/.test(data.sin)) {
      if (!fieldErrors.sin) fieldErrors.sin = [];
      fieldErrors.sin.push('Please enter a valid SIN format: 123-456-789');
    }

    // Postal code validation
    const postalFields = ['postalCode', 'personalPostalCode'];
    postalFields.forEach(field => {
      const value = data[field];
      if (value && !/^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/.test(value)) {
        if (!fieldErrors[field]) fieldErrors[field] = [];
        fieldErrors[field].push('Please enter a valid postal code format: A1A 1A1');
      }
    });

    setValidationErrors({ fieldErrors, crossFieldErrors, requiredFields });
    return Object.keys(fieldErrors).length === 0 && crossFieldErrors.length === 0;
  };

  // Fetch branches data
  React.useEffect(() => {
    const fetchBranches = async () => {
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1339/api';
        const response = await fetch(`${apiUrl}/branches?populate=*`);
        if (response.ok) {
          const data = await response.json();
          setBranches(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }
    };

    fetchBranches();
  }, []);

  // Check if signature is required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.brokerInfo;
    console.log('🔍 BROKER INFO FORM: isSignatureRequiredAndSaved check');
    console.log('🔍 BROKER INFO FORM: formData:', formData);
    console.log('🔍 BROKER INFO FORM: formData?.firstSaveComplete:', formData?.firstSaveComplete);
    console.log('🔍 BROKER INFO FORM: watchedValues.signature:', watchedValues.signature);
    console.log('🔍 BROKER INFO FORM: typeof watchedValues.signature:', typeof watchedValues.signature);

    // If firstSaveComplete is false, signature must be saved before form can be saved
    if (!formData?.firstSaveComplete) {
      const hasSignature = !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
      console.log('🔍 BROKER INFO FORM: firstSaveComplete is false, hasSignature:', hasSignature);
      return hasSignature;
    }
    // If firstSaveComplete is true, form can be saved regardless of signature
    console.log('🔍 BROKER INFO FORM: firstSaveComplete is true, returning true');
    return true;
  }, [forms.brokerInfo, watchedValues.signature]);

  // Handle form submission
  const onSubmit = async (data: BrokerInfoFormData) => {
    console.log('🚀 BROKER INFO FORM: onSubmit called with data:', data);
    console.log('🚀 BROKER INFO FORM: Form errors:', errors);
    console.log('🚀 BROKER INFO FORM: isSignatureRequiredAndSaved:', isSignatureRequiredAndSaved);

    setIsLoading(true);
    setProcessingStatus({ visible: true, status: 'loading', message: 'Updating Broker Information...' });

    try {
      // CRITICAL FIX: Preserve signature field from form context if it exists
      // This prevents overwriting the upload ID with a data URL
      const currentFormData = forms.brokerInfo || {};
      const preservedSignature = currentFormData.signature;

      // Prepare form data, preserving signature if it's already an upload ID
      const formDataToSubmit = {
        ...data,
        isFormComplete: true,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      };

      // Extract signature ID properly - handle both number and object formats
      let signatureId = null;
      if (preservedSignature) {
        if (typeof preservedSignature === 'number') {
          signatureId = preservedSignature;
        } else if (typeof preservedSignature === 'object' && preservedSignature !== null && 'id' in preservedSignature) {
          signatureId = (preservedSignature as any).id;
        }
      }

      // If we have a signature ID, use it; otherwise use the form data signature
      if (signatureId) {
        formDataToSubmit.signature = signatureId;
      } else if (data.signature && typeof data.signature === 'number') {
        formDataToSubmit.signature = data.signature;
      }

      // Debug logging
      console.log('🚀 BROKER INFO FORM: Signature handling debug:');
      console.log('🚀 BROKER INFO FORM: preservedSignature:', preservedSignature);
      console.log('🚀 BROKER INFO FORM: signatureId:', signatureId);
      console.log('🚀 BROKER INFO FORM: data.signature:', data.signature);
      console.log('🚀 BROKER INFO FORM: formDataToSubmit.signature:', formDataToSubmit.signature);
      console.log('🚀 BROKER INFO FORM: Complete form data to submit:', formDataToSubmit);

      // Update form data in context
      updateForm('brokerInfo', formDataToSubmit);

      setProcessingStatus({ visible: true, status: 'loading', message: 'Saving form...' });

      // Save to backend
      await saveForm('brokerInfo');

      // CRITICAL FIX: Also save relevant data to user profile
      try {
        setProcessingStatus({ visible: true, status: 'loading', message: 'Updating user profile...' });

        const userUpdateData = {
          firstname: data.firstName,
          middlename: data.middleName,
          lastname: data.lastName,
          workEmail: data.workEmail,
          workPhone: data.workPhone,
          phone: data.cellPhone,
          cellPhone: data.cellPhone,
          position: data.position,
          license: data.license,
          bio: data.bio,
          province: data.province,
          // Social media
          facebook: data.facebook,
          instagram: data.instagram,
          linkedin: data.linkedin,
          twitter: data.twitter,
          youtube: data.youtube,
        };

        // Filter out empty values
        const filteredUserData = Object.fromEntries(
          Object.entries(userUpdateData).filter(([_, value]) =>
            value !== undefined && value !== null && value !== ""
          )
        );

        if (Object.keys(filteredUserData).length > 0) {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userAuth.userInfo?.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${getCookie('jwt')}`,
            },
            body: JSON.stringify(filteredUserData),
          });

          if (response.ok) {
            const updatedUser = await response.json();
            // Update the auth context with the new user data
            if (updateUserData) {
              updateUserData(updatedUser);
            }
            console.log('User profile updated successfully:', filteredUserData);
          } else {
            console.warn('Failed to update user profile:', response.status);
          }
        }
      } catch (userUpdateError) {
        console.warn('Error updating user profile:', userUpdateError);
        // Don't fail the form submission if user update fails
      }

      setProcessingStatus({ visible: true, status: 'success', message: 'Broker Information form saved!' });
      toast.success("Broker information saved successfully!");

      // Hide success message after delay
      setTimeout(() => {
        setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);

    } catch (error) {
      console.error("Error saving broker information:", error);
      setProcessingStatus({ visible: true, status: 'error', message: 'Failed to save broker information' });
      toast.error("Failed to save broker information");

      // Hide error message after delay
      setTimeout(() => {
        setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle bio character count
  const handleBioChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, bio: value.length }));
    setValue('bio', value);
  };

  // Handle additional notes character count
  const handleAdditionalNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, additionalNotes: value.length }));
    setValue('additionalNotes', value);
  };

  // Handle mortgage software change
  const handleMortgageSoftwareChange = (value: string) => {
    setValue('mortgageSoftware', value);
    setShowOtherMortgageSoftware(value === "Other");
    if (value !== "Other") {
      setValue('otherMortgageSoftware', "");
    }
  };

  // Handle social media URL formatting
  const handleSocialMediaUrlChange = (field: keyof BrokerInfoFormData, value: string) => {
    const formattedUrl = formatUrl(value);
    setValue(field, formattedUrl);
  };

  // Handle social media toggle - simple Yes/No logic
  const handleSocialMediaToggle = (platform: string, checked: boolean) => {
    const toggleField = `has${platform}` as keyof BrokerInfoFormData;
    const urlField = platform.toLowerCase() as keyof BrokerInfoFormData;
    const handlerField = `${platform.toLowerCase()}Handler` as keyof BrokerInfoFormData;

    // Simple toggle: true = Yes (show fields), false = No (hide fields)
    setValue(toggleField, checked);

    // Clear URL and handler fields when set to No
    if (!checked) {
      setValue(urlField, "");
      setValue(handlerField, "");
    }
  };

  // Handle phone number formatting
  const handlePhoneChange = (field: string, value: string) => {
    const formattedPhone = formatPhoneNumber(value);
    setValue(field as keyof BrokerInfoFormData, formattedPhone);
  };

  // Format phone number helper
  const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  // Handle currency value formatting
  const handleCurrencyChange = (field: string, value: number | string) => {
    setValue(field as keyof BrokerInfoFormData, value.toString());
  };

  // Handle same address toggle
  const handleSameAddressChange = (checked: boolean) => {
    setValue('sameAddress', checked);
    if (checked) {
      setValue('personalAddress', watchedValues.address);
      setValue('personalSuiteUnit', watchedValues.suiteUnit);
      setValue('personalCity', watchedValues.city);
      setValue('personalProvince', watchedValues.province);
      setValue('personalPostalCode', watchedValues.postalCode);
    }
  };

  // Enhanced same address warning system
  React.useEffect(() => {
    if (!watchedValues.sameAddress) {
      const officeFields = ['address', 'suiteUnit', 'city', 'province', 'postalCode'];
      const personalFields = ['personalAddress', 'personalSuiteUnit', 'personalCity', 'personalProvince', 'personalPostalCode'];

      let hasMatchingFields = false;
      for (let i = 0; i < officeFields.length; i++) {
        const officeValue = watchedValues[officeFields[i] as keyof BrokerInfoFormData] as string;
        const personalValue = watchedValues[personalFields[i] as keyof BrokerInfoFormData] as string;

        if (officeValue && personalValue &&
            officeValue.replace(/\s/g, '').toLowerCase() === personalValue.replace(/\s/g, '').toLowerCase()) {
          hasMatchingFields = true;
          break;
        }
      }

      if (hasMatchingFields && sameAddressWarning.sameAddress === null) {
        setSameAddressWarning({ showMessage: true, sameAddress: false });
      }
    }
  }, [watchedValues.address, watchedValues.suiteUnit, watchedValues.city, watchedValues.province, watchedValues.postalCode,
      watchedValues.personalAddress, watchedValues.personalSuiteUnit, watchedValues.personalCity,
      watchedValues.personalProvince, watchedValues.personalPostalCode, watchedValues.sameAddress, sameAddressWarning.sameAddress]);

  // Handle same address warning response
  const handleSameAddressWarningResponse = (response: boolean) => {
    setSameAddressWarning({ showMessage: false, sameAddress: response });
    if (response) {
      setValue('sameAddress', true);
      setValue('personalAddress', watchedValues.address);
      setValue('personalSuiteUnit', watchedValues.suiteUnit);
      setValue('personalCity', watchedValues.city);
      setValue('personalProvince', watchedValues.province);
      setValue('personalPostalCode', watchedValues.postalCode);
    }
  };

  // Handle branch selection
  const handleBranchSelect = (addressInfo: any) => {
    // Capitalize province name to match form options
    const capitalizeProvince = (province: string) => {
      const provinceMap: Record<string, string> = {
        'alberta': 'Alberta',
        'british columbia': 'British Columbia',
        'manitoba': 'Manitoba',
        'new brunswick': 'New Brunswick',
        'newfoundland and labrador': 'Newfoundland and Labrador',
        'nova scotia': 'Nova Scotia',
        'ontario': 'Ontario',
        'prince edward island': 'Prince Edward Island',
        'quebec': 'Quebec',
        'saskatchewan': 'Saskatchewan',
        'northwest territories': 'Northwest Territories',
        'nunavut': 'Nunavut',
        'yukon': 'Yukon'
      };
      return provinceMap[province.toLowerCase()] || province;
    };
    
    setValue('address', addressInfo.address);
    setValue('suiteUnit', addressInfo.suiteUnit || "");
    setValue('city', addressInfo.city);
    setValue('province', capitalizeProvince(addressInfo.province));
    setValue('postalCode', addressInfo.postalCode);
    if (addressInfo.brokerageLicense) {
      setValue('brokerageLicense', addressInfo.brokerageLicense);
    }
    
    // Trigger form validation to clear any validation errors
    setTimeout(() => {
      form.trigger(['address', 'city', 'province', 'postalCode']);
    }, 100);
  };

  // Trigger validation when address fields change
  React.useEffect(() => {
    if (watchedValues.address && watchedValues.city && watchedValues.province && watchedValues.postalCode) {
      // Clear validation errors for address fields when they are populated
      form.clearErrors(['address', 'city', 'province', 'postalCode']);
    }
  }, [watchedValues.address, watchedValues.city, watchedValues.province, watchedValues.postalCode, form]);

  // Check if user is licensed (affects which fields to show)
  const isLicensed = userAuth.userInfo?.license !== undefined && userAuth.userInfo?.license !== null;

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    charCount,
    sameAddressWarning,
    branches,
    isLicensed,
    showOtherMortgageSoftware,
    processingStatus,
    formStatus,
    validationErrors,
    isSignatureRequiredAndSaved,
    validateForm,
    handleBioChange,
    handleAdditionalNotesChange,
    handleMortgageSoftwareChange,
    handleSocialMediaUrlChange,
    handleSocialMediaToggle,
    handlePhoneChange,
    handleCurrencyChange,
    handleSameAddressChange,
    handleSameAddressWarningResponse,
    handleBranchSelect,
    handleSubmit: handleSubmit((data) => {
      console.log('🚀 BROKER INFO FORM: handleSubmit wrapper called');
      return onSubmit(data);
    }, (errors) => {
      console.log('🚨 BROKER INFO FORM: Form validation failed with errors:', errors);
    }),
  };
}
