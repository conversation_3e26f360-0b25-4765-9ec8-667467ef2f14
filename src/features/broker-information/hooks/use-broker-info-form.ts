"use client";

import * as React from "react";
import { brokerInfoSchema, BrokerInfoFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { formatUrl } from "@/shared/lib/format-url";
import { getCookie } from "@/shared/lib/auth";

// Additional state interfaces for broker-specific functionality
interface CharCount {
  bio: number;
  additionalNotes: number;
}

interface SameAddressWarning {
  showMessage: boolean;
  sameAddress: boolean | null;
}

interface FormStatus {
  isComplete: boolean;
  completionPercentage: number;
}

interface ValidationErrors {
  requiredFields: Array<{ id: string; label: string }>;
  crossFieldErrors: Array<{ id: string; label: string; message: string }>;
}

export function useBrokerInfoForm() {
  const { userAuth, updateUserData } = useAuthContext();
  
  // Additional state for broker-specific functionality
  const [charCount, setCharCount] = React.useState<CharCount>({ bio: 0, additionalNotes: 0 });
  const [sameAddressWarning, setSameAddressWarning] = React.useState<SameAddressWarning>({
    showMessage: false,
    sameAddress: null,
  });
  const [branches, setBranches] = React.useState<any[]>([]);
  const [showOtherMortgageSoftware, setShowOtherMortgageSoftware] = React.useState(false);
  const [formStatus, setFormStatus] = React.useState<FormStatus>({
    isComplete: false,
    completionPercentage: 0
  });
  const [validationErrors] = React.useState<ValidationErrors>({
    requiredFields: [],
    crossFieldErrors: []
  });

  // User field mappings for auto-population
  const userFieldMappings = {
    firstName: 'firstname',
    middleName: 'middlename', 
    lastName: 'lastname',
    workEmail: 'workEmail',
    workPhone: 'workPhone',
    cellPhone: 'cellPhone',
    position: 'position',
    license: 'license',
    bio: 'bio',
    province: 'province',
    facebook: 'facebook',
    instagram: 'instagram',
    linkedin: 'linkedin',
    twitter: 'twitter',
    youtube: 'youtube',
  };

  // Transform function to handle special data processing
  const transformSubmitData = React.useCallback((data: BrokerInfoFormData) => {
    // Handle phone number formatting
    const phoneFields = ['workPhone', 'homePhone', 'cellPhone', 'emergencyPhone'];
    const transformedData = { ...data };
    
    phoneFields.forEach(field => {
      if (transformedData[field as keyof BrokerInfoFormData]) {
        // Apply phone formatting if needed
        // transformedData[field] = formatPhone(transformedData[field]);
      }
    });

    // Handle URL formatting
    const urlFields: (keyof BrokerInfoFormData)[] = ['facebook', 'instagram', 'linkedin', 'twitter', 'youtube', 'tiktok', 'pinterest', 'threads', 'bluesky'];
    urlFields.forEach(field => {
      const value = transformedData[field];
      if (value && typeof value === 'string') {
        (transformedData as any)[field] = formatUrl(value);
      }
    });

    return transformedData;
  }, []);

  // Success callback to update user profile
  const onSuccess = React.useCallback(async (data: BrokerInfoFormData) => {
    try {
      // Update user profile with relevant data
      const userUpdateData = {
        firstname: data.firstName,
        middlename: data.middleName,
        lastname: data.lastName,
        workEmail: data.workEmail,
        workPhone: data.workPhone,
        phone: data.cellPhone,
        cellPhone: data.cellPhone,
        position: data.position,
        license: data.license,
        bio: data.bio,
        province: data.province,
        facebook: data.facebook,
        instagram: data.instagram,
        linkedin: data.linkedin,
        twitter: data.twitter,
        youtube: data.youtube,
      };

      // Filter out empty values
      const filteredUserData = Object.fromEntries(
        Object.entries(userUpdateData).filter(([_, value]) =>
          value !== undefined && value !== null && value !== ""
        )
      );

      if (Object.keys(filteredUserData).length > 0) {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userAuth.userInfo?.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getCookie('jwt')}`,
          },
          body: JSON.stringify(filteredUserData),
        });

        if (response.ok) {
          const updatedUser = await response.json();
          if (updateUserData) {
            updateUserData(updatedUser);
          }
          console.log('User profile updated successfully:', filteredUserData);
        } else {
          console.warn('Failed to update user profile:', response.status);
        }
      }
    } catch (error) {
      console.warn('Error updating user profile:', error);
      // Don't fail the form submission if user update fails
    }
  }, [userAuth.userInfo?.id, updateUserData]);

  // Use the base form hook with broker-specific configuration
  const baseForm = useBaseForm<BrokerInfoFormData>({
    formName: 'brokerInfo',
    schema: brokerInfoSchema as any, // Type assertion to handle complex Zod schema
    transformSubmitData,
    onSuccess,
  });

  const { form, watchedValues } = baseForm;
  const { setValue, trigger } = form;

  // FIXED: Use enhanced data population hook to prevent infinite loops
  useFormDataPopulation('brokerInfo', form, {
    userFieldMappings,
  });

  // Load branches data
  React.useEffect(() => {
    const loadBranches = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/branches`);
        if (response.ok) {
          const data = await response.json();
          setBranches(data.data || []);
        }
      } catch (error) {
        console.error('Error loading branches:', error);
      }
    };
    loadBranches();
  }, []);

  // Update character counts when form values change
  React.useEffect(() => {
    if (watchedValues.bio) {
      setCharCount(prev => ({ ...prev, bio: watchedValues.bio?.length || 0 }));
    }
    if (watchedValues.additionalNotes) {
      setCharCount(prev => ({ ...prev, additionalNotes: watchedValues.additionalNotes?.length || 0 }));
    }
  }, [watchedValues.bio, watchedValues.additionalNotes]);

  // Update mortgage software conditional state
  React.useEffect(() => {
    setShowOtherMortgageSoftware(watchedValues.mortgageSoftware === "Other");
  }, [watchedValues.mortgageSoftware]);

  // Calculate form completion percentage
  const calculateCompletionPercentage = React.useCallback((data: BrokerInfoFormData): number => {
    const requiredFields = [
      'firstName', 'lastName', 'position', 'workEmail', 'address', 'city', 'province', 'postalCode',
      'personalAddress', 'personalCity', 'personalProvince', 'personalPostalCode',
      'workPhone', 'birthdate', 'sin', 'emergencyContact', 'emergencyPhone', 'bio'
    ];

    // Add Saskatchewan-specific field
    if (data.province === "Saskatchewan") {
      requiredFields.push('brokerageLicense');
    }

    // Add declaration fields for licensed users
    const isLicensed = userAuth.userInfo?.licensed === true;
    if (isLicensed) {
      requiredFields.push('declarationRegulatoryReview', 'declarationClientComplaints');
      if (data.declarationRegulatoryReview === true) {
        requiredFields.push('declarationRegulatoryReviewDetails');
      }
      if (data.declarationClientComplaints === true) {
        requiredFields.push('declarationClientComplaintsDetails');
      }
    }

    const completedFields = requiredFields.filter(field => {
      const value = data[field as keyof BrokerInfoFormData];
      return value !== undefined && value !== null && value !== '';
    });

    return Math.round((completedFields.length / requiredFields.length) * 100);
  }, [userAuth.userInfo?.licensed]);

  // Update form status when watched values change
  React.useEffect(() => {
    const completionPercentage = calculateCompletionPercentage(watchedValues);
    setFormStatus({
      isComplete: completionPercentage === 100,
      completionPercentage
    });
  }, [watchedValues, calculateCompletionPercentage]);

  // Derived values
  const isLicensed = userAuth.userInfo?.licensed === true;
  
  // Check if signature is required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    // If firstSaveComplete is false, signature must be saved before form can be saved
    if (!watchedValues.firstSaveComplete) {
      return !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    }
    // If firstSaveComplete is true, form can be saved regardless of signature
    return true;
  }, [watchedValues.firstSaveComplete, watchedValues.signature]);

  // Handler methods
  const handleBioChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, bio: value.length }));
    setValue('bio', value);
  }, [setValue]);

  const handleAdditionalNotesChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, additionalNotes: value.length }));
    setValue('additionalNotes', value);
  }, [setValue]);

  const handleMortgageSoftwareChange = React.useCallback((value: string) => {
    setValue('mortgageSoftware', value);
    setShowOtherMortgageSoftware(value === "Other");
    if (value !== "Other") {
      setValue('otherMortgageSoftware', "");
    }
  }, [setValue]);

  const handleSocialMediaUrlChange = React.useCallback((field: keyof BrokerInfoFormData, value: string) => {
    const formattedUrl = formatUrl(value);
    setValue(field, formattedUrl);
  }, [setValue]);

  const handleSocialMediaToggle = React.useCallback((platform: string, checked: boolean) => {
    const toggleField = `has${platform}` as keyof BrokerInfoFormData;
    const urlField = platform.toLowerCase() as keyof BrokerInfoFormData;
    const handlerField = `${platform.toLowerCase()}Handler` as keyof BrokerInfoFormData;

    setValue(toggleField, checked);

    if (!checked) {
      setValue(urlField, "");
      setValue(handlerField, "");
    }
  }, [setValue]);

  const handlePhoneChange = React.useCallback((field: keyof BrokerInfoFormData, value: string) => {
    setValue(field, value);
  }, [setValue]);

  const handleCurrencyChange = React.useCallback((field: keyof BrokerInfoFormData, value: string) => {
    setValue(field, value);
  }, [setValue]);

  const handleSameAddressChange = React.useCallback((checked: boolean) => {
    setValue('sameAddress', checked);

    if (checked) {
      // Copy office address to personal address
      setValue('personalAddress', watchedValues.address || "");
      setValue('personalSuiteUnit', watchedValues.suiteUnit || "");
      setValue('personalCity', watchedValues.city || "");
      setValue('personalProvince', watchedValues.province || "");
      setValue('personalPostalCode', watchedValues.postalCode || "");
    } else {
      // Show warning if addresses were the same
      const addressesAreSame =
        watchedValues.personalAddress === watchedValues.address &&
        watchedValues.personalCity === watchedValues.city &&
        watchedValues.personalProvince === watchedValues.province &&
        watchedValues.personalPostalCode === watchedValues.postalCode;

      if (addressesAreSame) {
        setSameAddressWarning({
          showMessage: true,
          sameAddress: false
        });
      }
    }
  }, [setValue, watchedValues]);

  const handleSameAddressWarningResponse = React.useCallback((keepSame: boolean) => {
    if (!keepSame) {
      // Clear personal address fields
      setValue('personalAddress', "");
      setValue('personalSuiteUnit', "");
      setValue('personalCity', "");
      setValue('personalProvince', "");
      setValue('personalPostalCode', "");
    }

    setSameAddressWarning({
      showMessage: false,
      sameAddress: null
    });
  }, [setValue]);

  const handleBranchSelect = React.useCallback((addressInfo: { address: string; city: string; province: string; postalCode: string }) => {
    setValue('address', addressInfo.address);
    setValue('suiteUnit', ""); // AddressSelect doesn't provide suiteUnit
    setValue('city', addressInfo.city);
    setValue('province', addressInfo.province);
    setValue('postalCode', addressInfo.postalCode);

    // Trigger form validation
    setTimeout(() => {
      trigger(['address', 'city', 'province', 'postalCode']);
    }, 100);
  }, [setValue, trigger]);

  const validateForm = React.useCallback((_data: BrokerInfoFormData) => {
    // This could be enhanced with more sophisticated validation
    // For now, rely on Zod schema validation
    return {
      requiredFields: [],
      crossFieldErrors: []
    };
  }, []);

  return {
    // Base form functionality
    ...baseForm,

    // Broker-specific state
    charCount,
    sameAddressWarning,
    branches,
    isLicensed,
    showOtherMortgageSoftware,
    formStatus,
    validationErrors,
    isSignatureRequiredAndSaved,

    // Handler methods
    validateForm,
    handleBioChange,
    handleAdditionalNotesChange,
    handleMortgageSoftwareChange,
    handleSocialMediaUrlChange,
    handleSocialMediaToggle,
    handlePhoneChange,
    handleCurrencyChange,
    handleSameAddressChange,
    handleSameAddressWarningResponse,
    handleBranchSelect,
  };
}
