import z from "zod";

// Form validation schema
const brokerInfoSchema = z.object({
  // Personal Information
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  legalName: z.string().optional(),
  preferredName: z.string().optional(),
  titles: z.string().optional(),
  position: z.string().min(1, "Position is required"),
  license: z.string().optional(),
  birthdate: z.string().min(1, "Birthdate is required"),
  sin: z.string().min(1, "SIN is required"),
  tshirtSize: z.string().optional(),
  bio: z.string().min(1, "Bio is required"),
  additionalNotes: z.string().optional(),

  // Contact Information
  workEmail: z.string().email("Valid email is required").min(1, "Work email is required"),
  workPhone: z.string().min(1, "Work phone is required"),
  ext: z.string().optional(),
  homePhone: z.string().optional(),
  cellPhone: z.string().optional(),
  emergencyContact: z.string().min(1, "Emergency contact is required"),
  emergencyPhone: z.string().min(1, "Emergency phone is required"),

  // Office Address fields
  address: z.string().min(1, "Address is required"),
  suiteUnit: z.string().optional(),
  city: z.string().min(1, "City is required"),
  province: z.string().min(1, "Province is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  brokerageLicense: z.string().optional(), // Required for Saskatchewan

  // Personal address fields
  personalAddress: z.string().min(1, "Personal address is required"),
  personalSuiteUnit: z.string().optional(),
  personalCity: z.string().min(1, "Personal city is required"),
  personalProvince: z.string().min(1, "Personal province is required"),
  personalPostalCode: z.string().min(1, "Personal postal code is required"),
  sameAddress: z.boolean().default(false),

  // Brokering Background
  existingAgent: z.boolean().optional(),
  mortgageSoftware: z.string().optional(),
  otherMortgageSoftware: z.string().optional(),
  lender1: z.string().optional(),
  lender1Volume: z.string().optional(),
  lender2: z.string().optional(),
  lender2Volume: z.string().optional(),
  lender3: z.string().optional(),
  lender3Volume: z.string().optional(),

  // Enhanced Social Media with toggles
  hasFacebook: z.boolean().default(false),
  facebook: z.string().optional(),
  facebookHandler: z.string().optional(),
  hasInstagram: z.boolean().default(false),
  instagram: z.string().optional(),
  instagramHandler: z.string().optional(),
  hasLinkedin: z.boolean().default(false),
  linkedin: z.string().optional(),
  linkedinHandler: z.string().optional(),
  hasYoutube: z.boolean().default(false),
  youtube: z.string().optional(),
  youtubeHandler: z.string().optional(),
  hasTwitter: z.boolean().default(false),
  twitter: z.string().optional(),
  twitterHandler: z.string().optional(),
  hasTikTok: z.boolean().default(false),
  tiktok: z.string().optional(),
  tiktokHandler: z.string().optional(),
  hasPinterest: z.boolean().default(false),
  pinterest: z.string().optional(),
  pinterestHandler: z.string().optional(),
  hasThreads: z.boolean().default(false),
  threads: z.string().optional(),
  threadsHandler: z.string().optional(),
  hasBluesky: z.boolean().default(false),
  bluesky: z.string().optional(),
  blueskyHandler: z.string().optional(),

  // Declaration fields (for licensed brokers)
  declarationRegulatoryReview: z.boolean().optional(),
  declarationClientComplaints: z.boolean().optional(),
  declarationRegulatoryReviewDetails: z.string().optional(),
  declarationClientComplaintsDetails: z.string().optional(),

  // Signature
  signature: z.string().optional(),

  // Form metadata
  isFormComplete: z.boolean().optional(),
  firstSaveComplete: z.boolean().optional(),
  lastUpdated: z.string().optional(),
}).refine((data) => {
  // Saskatchewan requires brokerage license
  if (data.province === "Saskatchewan" && !data.brokerageLicense) {
    return false;
  }
  return true;
}, {
  message: "Brokerage license is required for Saskatchewan",
  path: ["brokerageLicense"],
}).refine((data) => {
  // If "Other" mortgage software is selected, otherMortgageSoftware is required
  if (data.mortgageSoftware === "Other" && !data.otherMortgageSoftware) {
    return false;
  }
  return true;
}, {
  message: "Please specify other mortgage software",
  path: ["otherMortgageSoftware"],
}).refine((data) => {
  // If declarationRegulatoryReview is true, details are required
  if (data.declarationRegulatoryReview === true && !data.declarationRegulatoryReviewDetails) {
    return false;
  }
  return true;
}, {
  message: "Please provide details for regulatory review",
  path: ["declarationRegulatoryReviewDetails"],
}).refine((data) => {
  // If declarationClientComplaints is true, details are required
  if (data.declarationClientComplaints === true && !data.declarationClientComplaintsDetails) {
    return false;
  }
  return true;
}, {
  message: "Please provide details for client complaints",
  path: ["declarationClientComplaintsDetails"],
});

export type BrokerInfoFormData = z.infer<typeof brokerInfoSchema>;
export { brokerInfoSchema };