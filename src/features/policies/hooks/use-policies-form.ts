"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { policiesSchema, PoliciesFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";

export function usePoliciesForm() {
  const { forms, updateForm, saveForm } = useFormsContext();
  const { userAuth } = useAuthContext();
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<PoliciesFormData>({
    resolver: zodResolver(policiesSchema) as any,
    defaultValues: {
      brokerName: (forms.policiesAndProcedure as any)?.brokerName || userAuth.userInfo?.firstname + " " + userAuth.userInfo?.lastname || "",
      signature: (forms.policiesAndProcedure as any)?.signature || "",
      signatureDate: (forms.policiesAndProcedure as any)?.signatureDate || "",
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  // Load existing form data and populate with user data if firstSaveComplete is false
  React.useEffect(() => {
    if (userAuth.userInfo && forms.policiesAndProcedure) {
      const formData = forms.policiesAndProcedure;
      const user = userAuth.userInfo;

      // Check if this is the first time saving this form
      if (!formData.firstSaveComplete && !formData.userDataPopulated) {
        console.log('🔄 POLICIES: First save not complete, populating with user data');

        // Map user data to form fields
        const userDataMappings = {
          brokerName: `${user.firstname || ''} ${user.lastname || ''}`.trim(),
        };

        // Set form values
        Object.entries(userDataMappings).forEach(([field, value]) => {
          if (value && typeof value === 'string' && value.trim() !== "") {
            setValue(field as keyof PoliciesFormData, value as any);
          }
        });

        // CRITICAL: Also update the forms context with the user data
        // This ensures that one-click signature can access the pre-populated data
        // Add a flag to prevent infinite loops
        updateForm('policiesAndProcedure', {
          ...formData,
          ...userDataMappings,
          userDataPopulated: true, // Flag to prevent re-population
        });

        console.log('🔄 POLICIES: Populated form and context with user data:', userDataMappings);
      } else {
        console.log('🔄 POLICIES: First save complete or user data already populated, using saved form data');
      }

      // Define the form fields that should be loaded
      const formFields: (keyof PoliciesFormData)[] = [
        'brokerName', 'signature', 'signatureDate'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });
    }
  }, [userAuth.userInfo?.id, forms.policiesAndProcedure?.firstSaveComplete, forms.policiesAndProcedure?.userDataPopulated, setValue, updateForm]);

  // Check if signature exists and is saved
  const hasSignature = () => {
    const signature = watchedValues.signature;
    if (!signature) return false;
    
    // If it's a string, check if it's not empty
    if (typeof signature === 'string') {
      return signature.length > 0;
    }
    
    // If it's an object, check if it has a url
    if (typeof signature === 'object' && signature.url) {
      return true;
    }
    
    return false;
  };

  // Check if signature is required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.policiesAndProcedure;
    // If firstSaveComplete is false, signature must be saved before form can be saved
    if (!formData?.firstSaveComplete) {
      return !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    }
    // If firstSaveComplete is true, form can be saved regardless of signature
    return true;
  }, [forms.policiesAndProcedure, watchedValues.signature]);

  // Handle form submission
  const onSubmit = async (data: PoliciesFormData) => {
    setIsLoading(true);
    try {
      // CRITICAL FIX: Preserve signature field from form context if it exists
      // This prevents overwriting the upload ID with a data URL
      const currentFormData = forms.policiesAndProcedure || {};
      const preservedSignature = currentFormData.signature;

      const formDataToSubmit = {
        ...data,
        signatureDate: new Date().toISOString(),
        isFormComplete: true,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      };

      // Extract signature ID properly - handle both number and object formats
      let signatureId = null;
      if (preservedSignature) {
        if (typeof preservedSignature === 'number') {
          signatureId = preservedSignature;
        } else if (typeof preservedSignature === 'object' && preservedSignature !== null && 'id' in preservedSignature) {
          signatureId = (preservedSignature as any).id;
        }
      }

      // If we have a signature ID, use it; otherwise use the form data signature
      if (signatureId) {
        formDataToSubmit.signature = signatureId;
      } else if (data.signature && typeof data.signature === 'number') {
        formDataToSubmit.signature = data.signature;
      }

      // Update form data in context
      updateForm('policiesAndProcedure', formDataToSubmit);

      // Save to backend
      await saveForm('policiesAndProcedure');

      toast.success("Policies and procedures form saved successfully!");
    } catch (error) {
      console.error("Error saving policies form:", error);
      toast.error("Failed to save policies form");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    hasSignature: hasSignature(),
    isSignatureRequiredAndSaved,
    handleSubmit: handleSubmit(onSubmit),
  };
}