"use client";

import * as React from "react";
import { policiesSchema, PoliciesFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { useFormsContext } from "@/shared/contexts/forms-context";

export function usePoliciesForm() {
  const { userAuth } = useAuthContext();
  const { forms } = useFormsContext();

  // User field mappings for auto-population
  const userFieldMappings = {
    brokerName: 'firstname', // Will be combined with lastname
  };

  // Transform function to handle broker name combination
  const transformSubmitData = React.useCallback((data: PoliciesFormData) => {
    // Combine first and last name for broker name if not already set
    if (!data.brokerName && userAuth.userInfo) {
      const firstName = userAuth.userInfo.firstname || '';
      const lastName = userAuth.userInfo.lastname || '';
      data.brokerName = `${firstName} ${lastName}`.trim();
    }
    return data;
  }, [userAuth.userInfo]);

  // Use the base form hook
  const baseForm = useBaseForm<PoliciesFormData>({
    formName: 'policiesAndProcedure',
    schema: policiesSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: {
      brokerName: "",
      signature: "",
      signatureDate: "",
    },
    transformSubmitData,
  });

  const { form, watchedValues } = baseForm;
  const { setValue } = form;

  // FIXED: Use enhanced data population hook to prevent infinite loops
  useFormDataPopulation('policiesAndProcedure', form, {
    userFieldMappings,
    customPopulationLogic: React.useCallback((formData: any, userData: any, form: any) => {
      // Set combined broker name
      const firstName = userData.firstname || '';
      const lastName = userData.lastname || '';
      const fullName = `${firstName} ${lastName}`.trim();
      if (fullName) {
        setValue('brokerName', fullName);
      }
    }, [setValue]),
  });

  // Check if signature exists and is saved
  const hasSignature = () => {
    const signature = watchedValues.signature;
    if (!signature) return false;
    
    // If it's a string, check if it's not empty
    if (typeof signature === 'string') {
      return signature.length > 0;
    }
    
    // If it's an object, check if it has a url
    if (typeof signature === 'object' && signature.url) {
      return true;
    }
    
    return false;
  };

  // FIXED: Correct signature flow logic
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.policiesAndProcedure;
    const hasSignature = !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    
    // CRITICAL FIX: Check firstSaveComplete from context data, not watchedValues
    if (!formData?.firstSaveComplete) {
      // First save: require signature to be present
      return hasSignature;
    }
    
    // Subsequent saves: always allow submission (form has been saved before)
    return true;
  }, [forms.policiesAndProcedure?.firstSaveComplete, watchedValues.signature]);

  return {
    // Base form functionality
    ...baseForm,

    // Policies-specific state
    hasSignature: hasSignature(),
    isSignatureRequiredAndSaved,
  };
}