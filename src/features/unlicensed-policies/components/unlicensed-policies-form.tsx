"use client";

import * as React from "react";
import { FileText, Edit } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { FormSectionTitle } from "@/shared/ui/form-section-title";
import { SignatureSection } from "@/shared/components/signature-section";
import { NextPrevFooter } from "@/shared/ui/next-prev-footer";
import { useUnlicensedPoliciesForm } from "../hooks/use-unlicensed-policies-form";

export function UnlicensedPoliciesForm() {
  const {
    form,
    watchedValues,
    errors,
    isLoading,
    complianceManualUrl,
    hasSignature,
    isSignatureRequiredAndSaved,
    handleSubmit,
    onSubmit,
  } = useUnlicensedPoliciesForm();

  const { setValue } = form;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-2xl font-bold mb-6">Policies and Procedures</h1>
      <Card>        
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Policies Acknowledgment */}
            <div className="space-y-4">
              <p>
                I acknowledge that I have read the <strong>Indi Mortgage</strong> (the "brokerage"){' '}
                <a
                  href={complianceManualUrl || "/documents/unlicensed-assistant-policies.pdf"}
                  target="_blank"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Unlicensed Assistant Policies and Procedures Manual
                </a>{' '}
                (the "Manual") in its entirety. I confirm that I will adhere to the practices and procedures
                contained within.
              </p>
              <p>
                I also acknowledge it is my sole responsibility to keep updated on any changes made to the Manual
                and that changes made will be communicated to me by the brokerage.
              </p>
            </div>

            {/* Broker Name Section */}
            <div>
              <Label htmlFor="brokerName" className="text-base font-medium mb-4 block">
                Broker/Agent Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="brokerName"
                {...form.register("brokerName")}
                placeholder="Broker/Agent Name*"
                className={errors.brokerName ? "border-red-500" : ""}
              />
              {errors.brokerName && (
                <p className="text-red-500 text-sm mt-1">{errors.brokerName.message}</p>
              )}
            </div>

            {/* Signature Section */}
            <div>
              <Label className="text-base font-medium mb-4 block">Signature</Label>
              <SignatureSection
                value={watchedValues.signature}
                onSignatureChange={(signature) => setValue('signature', signature as any)}
                setValue={setValue as any}
                form={form}
                fieldName="signature"
                formName="unlicensedPolicies"
                label="Your Signature"
                required={true}
              />
              {errors.signature && (
                <p className="text-red-500 text-sm mt-2">
                  {String(errors.signature.message || 'Signature is required')}
                </p>
              )}
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                variant="default"
                disabled={isLoading || !isSignatureRequiredAndSaved}
              >
                {isLoading ? "Saving..." : !isSignatureRequiredAndSaved ? "Save Signature First" : "Save Form"}
              </Button>
            </div>

            {/* Navigation Footer */}
            <NextPrevFooter />
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
