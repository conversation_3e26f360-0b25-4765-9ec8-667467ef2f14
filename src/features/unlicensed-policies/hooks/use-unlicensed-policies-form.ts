"use client";

import * as React from "react";
import { unlicensedPoliciesSchema, UnlicensedPoliciesFormData } from "../lib/validation";
import { useBaseForm } from "@/shared/hooks/use-base-form";
import { useFormDataPopulation } from "@/shared/hooks/use-form-data-population";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { useFormsContext } from "@/shared/contexts/forms-context";

export function useUnlicensedPoliciesForm() {
  const { userAuth } = useAuthContext();
  const { forms } = useFormsContext();
  const [complianceManualUrl, setComplianceManualUrl] = React.useState("");

  // User field mappings for auto-population
  const userFieldMappings = {
    brokerName: 'firstname', // Will be combined with lastname
  };

  // Transform function to handle broker name combination
  const transformSubmitData = React.useCallback((data: UnlicensedPoliciesFormData) => {
    // Combine first and last name for broker name if not already set
    if (!data.brokerName && userAuth.userInfo) {
      const firstName = userAuth.userInfo.firstname || '';
      const lastName = userAuth.userInfo.lastname || '';
      data.brokerName = `${firstName} ${lastName}`.trim();
    }
    return data;
  }, [userAuth.userInfo]);

  // Use the base form hook
  const baseForm = useBaseForm<UnlicensedPoliciesFormData>({
    formName: 'unlicensedPolicies',
    schema: unlicensedPoliciesSchema as any, // Type assertion for Zod schema compatibility
    defaultValues: {
      brokerName: "",
      signature: "",
      signatureDate: "",
    },
    transformSubmitData,
  });

  const { form, watchedValues } = baseForm;
  const { setValue } = form;

  // FIXED: Use enhanced data population hook to prevent infinite loops
  useFormDataPopulation('unlicensedPolicies', form, {
    userFieldMappings,
    customPopulationLogic: React.useCallback((formData: any, userData: any, form: any) => {
      // Set combined broker name
      const firstName = userData.firstname || '';
      const lastName = userData.lastname || '';
      const fullName = `${firstName} ${lastName}`.trim();
      if (fullName) {
        setValue('brokerName', fullName);
      }
    }, [setValue]),
  });

  // Load compliance manual URL
  React.useEffect(() => {
    const fetchComplianceManual = async () => {
      try {
        const response = await fetch('/api/documents');
        if (response.ok) {
          const data = await response.json();
          const unlicensedPolicy = data.find((doc: any) => doc.unlicensedPolicyProcedure === true);
          if (unlicensedPolicy?.file?.url) {
            setComplianceManualUrl(unlicensedPolicy.file.url);
          }
        }
      } catch (error) {
        console.error('Error fetching compliance manual:', error);
      }
    };
    fetchComplianceManual();
  }, []);

  // FIXED: Correct signature flow logic
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.unlicensedPolicies;
    const hasSignature = !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    
    // CRITICAL FIX: Check firstSaveComplete from context data, not watchedValues
    if (!formData?.firstSaveComplete) {
      // First save: require signature to be present
      return hasSignature;
    }
    
    // Subsequent saves: always allow submission (form has been saved before)
    return true;
  }, [forms.unlicensedPolicies?.firstSaveComplete, watchedValues.signature]);


  // Check if signature exists
  const hasSignature = () => {
    const signature = watchedValues.signature;
    if (!signature) return false;
    if (typeof signature === 'string') {
      return signature.length > 0;
    }
    if (typeof signature === 'object' && signature.url) {
      return true;
    }
    return false;
  };

  return {
    // Base form functionality
    ...baseForm,

    // Unlicensed policies specific state
    complianceManualUrl,
    hasSignature,
    isSignatureRequiredAndSaved,
  };
}
