"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { unlicensedPoliciesSchema, UnlicensedPoliciesFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";

export function useUnlicensedPoliciesForm() {
  const { userAuth } = useAuthContext();
  const { forms, updateForm, saveForm } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [complianceManualUrl, setComplianceManualUrl] = React.useState("");

  const form = useForm<UnlicensedPoliciesFormData>({
    resolver: zodResolver(unlicensedPoliciesSchema) as any,
    defaultValues: {
      brokerName: (forms.unlicensedPolicies as any)?.brokerName || userAuth.userInfo?.firstname + " " + userAuth.userInfo?.lastname || "",
      signature: (forms.unlicensedPolicies as any)?.signature || "",
      signatureDate: (forms.unlicensedPolicies as any)?.signatureDate || "",
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  // Load existing form data and populate with user data if firstSaveComplete is false
  React.useEffect(() => {
    if (userAuth.userInfo && forms.unlicensedPolicies) {
      const formData = forms.unlicensedPolicies;
      const user = userAuth.userInfo;

      // Check if this is the first time saving this form
      if (!formData.firstSaveComplete && !formData.userDataPopulated) {
        console.log('🔄 UNLICENSED POLICIES: First save not complete, populating with user data');

        // Map user data to form fields
        const userDataMappings = {
          brokerName: `${user.firstname || ''} ${user.lastname || ''}`.trim(),
        };

        // Set form values
        Object.entries(userDataMappings).forEach(([field, value]) => {
          if (value && typeof value === 'string' && value.trim() !== "") {
            setValue(field as keyof UnlicensedPoliciesFormData, value as any);
          }
        });

        // CRITICAL: Also update the forms context with the user data
        // This ensures that one-click signature can access the pre-populated data
        // Add a flag to prevent infinite loops
        updateForm('unlicensedPolicies', {
          ...formData,
          ...userDataMappings,
          userDataPopulated: true, // Flag to prevent re-population
        });

        console.log('🔄 UNLICENSED POLICIES: Populated form and context with user data:', userDataMappings);
      } else {
        console.log('🔄 UNLICENSED POLICIES: First save complete or user data already populated, using saved form data');
      }

      // Define the form fields that should be loaded
      const formFields: (keyof UnlicensedPoliciesFormData)[] = [
        'brokerName', 'signature', 'signatureDate'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });
    }
  }, [userAuth.userInfo?.id, forms.unlicensedPolicies?.firstSaveComplete, forms.unlicensedPolicies?.userDataPopulated, setValue, updateForm]);

  // Load compliance manual URL
  React.useEffect(() => {
    const fetchComplianceManual = async () => {
      try {
        const response = await fetch('/api/documents');
        if (response.ok) {
          const data = await response.json();
          const unlicensedPolicy = data.find((doc: any) => doc.unlicensedPolicyProcedure === true);
          if (unlicensedPolicy?.file?.url) {
            setComplianceManualUrl(unlicensedPolicy.file.url);
          }
        }
      } catch (error) {
        console.error('Error fetching compliance manual:', error);
      }
    };
    fetchComplianceManual();
  }, []);

  // Check if signature is required and saved
  const isSignatureRequiredAndSaved = React.useMemo(() => {
    const formData = forms.unlicensedPolicies;
    // If firstSaveComplete is false, signature must be saved before form can be saved
    if (!formData?.firstSaveComplete) {
      return !!(watchedValues.signature && (typeof watchedValues.signature === 'number' || watchedValues.signature !== ''));
    }
    // If firstSaveComplete is true, form can be saved regardless of signature
    return true;
  }, [forms.unlicensedPolicies, watchedValues.signature]);

  // Handle form submission
  const onSubmit = async (data: UnlicensedPoliciesFormData) => {
    setIsLoading(true);
    try {
      // CRITICAL FIX: Preserve signature field from form context if it exists
      // This prevents overwriting the upload ID with a data URL
      const currentFormData = forms.unlicensedPolicies || {};
      const preservedSignature = currentFormData.signature;

      const formDataToSubmit = {
        ...data,
        isFormComplete: true,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      };

      // Extract signature ID properly - handle both number and object formats
      let signatureId = null;
      if (preservedSignature) {
        if (typeof preservedSignature === 'number') {
          signatureId = preservedSignature;
        } else if (typeof preservedSignature === 'object' && preservedSignature !== null && 'id' in preservedSignature) {
          signatureId = (preservedSignature as any).id;
        }
      }

      // If we have a signature ID, use it; otherwise use the form data signature
      if (signatureId) {
        formDataToSubmit.signature = signatureId;
      } else if (data.signature && typeof data.signature === 'number') {
        formDataToSubmit.signature = data.signature;
      }

      // Update form data in context
      updateForm('unlicensedPolicies', formDataToSubmit);

      // Save to backend
      await saveForm('unlicensedPolicies');

      toast.success("Unlicensed policies saved successfully!");
    } catch (error) {
      console.error("Error saving unlicensed policies:", error);
      toast.error("Failed to save unlicensed policies");
    } finally {
      setIsLoading(false);
    }
  };

  // Check if signature exists
  const hasSignature = () => {
    const signature = watchedValues.signature;
    if (!signature) return false;
    if (typeof signature === 'string') {
      return signature.length > 0;
    }
    if (typeof signature === 'object' && signature.url) {
      return true;
    }
    return false;
  };

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    complianceManualUrl,
    hasSignature,
    isSignatureRequiredAndSaved,
    handleSubmit,
    onSubmit,
  };
}
