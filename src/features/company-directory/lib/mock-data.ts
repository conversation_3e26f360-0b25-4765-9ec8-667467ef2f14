/**
 * Mock data for company directory
 */

interface MockUser {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  phone?: string;
  position?: string;
  photo?: {
    url?: string;
    formats?: {
      thumbnail?: {
        url?: string;
      };
    };
  };
  province?: string;
  isStaffMember?: boolean;
}

interface MockDirectoryParams {
  start?: number;
  limit?: number;
  province?: string;
  search?: string;
  isStaffMember?: boolean;
}

interface MockDirectoryResult {
  users: MockUser[];
  count: number;
}

const mockUsers: MockUser[] = [
  {
    id: "1",
    firstname: "<PERSON>",
    lastname: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    position: "Mortgage Broker",
    province: "ON",
    isStaffMember: false
  },
  {
    id: "2",
    firstname: "<PERSON>",
    lastname: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    position: "Mortgage Agent",
    province: "<PERSON>",
    isStaffMember: false
  },
  {
    id: "3",
    firstname: "<PERSON>",
    lastname: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    position: "Mortgage Specialist",
    province: "AB",
    isStaffMember: true
  }
];

export function getMockDirectoryData(params: MockDirectoryParams = {}): MockDirectoryResult {
  const {
    start = 0,
    limit = 10,
    province,
    search,
    isStaffMember = false
  } = params;

  let filteredUsers = [...mockUsers];

  // Filter by province if specified
  if (province) {
    filteredUsers = filteredUsers.filter(user => user.province === province);
  }

  // Filter by search term if specified
  if (search) {
    const searchLower = search.toLowerCase();
    filteredUsers = filteredUsers.filter(user =>
      user.firstname.toLowerCase().includes(searchLower) ||
      user.lastname.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      user.position?.toLowerCase().includes(searchLower)
    );
  }

  // Apply pagination
  const paginatedUsers = filteredUsers.slice(start, start + limit);

  return {
    users: paginatedUsers,
    count: filteredUsers.length
  };
}
