/**
 * URL utility functions
 */

/**
 * Formats a URL to ensure it has a protocol
 */
export function formatUrl(url: string): string {
  if (!url || url.trim() === '') {
    return '';
  }

  const trimmedUrl = url.trim();
  
  // If it already has a protocol, return as is
  if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
    return trimmedUrl;
  }
  
  // Add https:// by default
  return `https://${trimmedUrl}`;
}

/**
 * Validates if a string is a valid URL
 */
export function isValidUrl(url: string): boolean {
  if (!url || url.trim() === '') {
    return true; // Empty URLs are considered valid (optional fields)
  }

  try {
    const formattedUrl = formatUrl(url);
    new URL(formattedUrl);
    return true;
  } catch {
    return false;
  }
}

/**
 * Extracts domain from URL
 */
export function getDomainFromUrl(url: string): string {
  try {
    const formattedUrl = formatUrl(url);
    const urlObj = new URL(formattedUrl);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

/**
 * Checks if URL is accessible (basic format check)
 */
export function isAccessibleUrl(url: string): boolean {
  if (!url) return false;
  
  const formattedUrl = formatUrl(url);
  
  // Basic checks
  if (!formattedUrl.includes('.')) return false;
  if (formattedUrl.length < 4) return false;
  
  return isValidUrl(formattedUrl);
}
