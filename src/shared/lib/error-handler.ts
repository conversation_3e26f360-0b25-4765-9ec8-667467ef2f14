/**
 * Comprehensive error handling utilities
 */

export interface ErrorDetails {
  code?: string;
  message: string;
  details?: any;
  timestamp: string;
  context?: string;
  userId?: string;
  userAgent?: string;
  url?: string;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
  reportToService?: boolean;
  fallbackMessage?: string;
  context?: string;
}

/**
 * Error types for better categorization
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  FILE_UPLOAD = 'FILE_UPLOAD',
  FORM_SUBMISSION = 'FORM_SUBMISSION',
  API = 'API',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Enhanced error class with additional context
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly code?: string;
  public readonly details?: any;
  public readonly timestamp: string;
  public readonly context?: string;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string,
    details?: any,
    context?: string
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.context = context;
  }
}

/**
 * Error handler class with comprehensive error management
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorQueue: ErrorDetails[] = [];
  private maxQueueSize = 100;

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle error with comprehensive logging and user feedback
   */
  async handleError(
    error: Error | AppError | string,
    options: ErrorHandlerOptions = {}
  ): Promise<void> {
    const {
      showToast = true,
      logToConsole = true,
      reportToService = false,
      fallbackMessage = 'An unexpected error occurred',
      context
    } = options;

    // Normalize error
    const normalizedError = this.normalizeError(error, context);
    
    // Log to console if enabled
    if (logToConsole) {
      console.error('Error handled:', normalizedError);
    }

    // Add to error queue
    this.addToQueue(normalizedError);

    // Show user feedback if enabled
    if (showToast) {
      const userMessage = this.getUserFriendlyMessage(normalizedError, fallbackMessage);
      this.showUserFeedback(userMessage, normalizedError.type);
    }

    // Report to error service if enabled
    if (reportToService) {
      await this.reportError(normalizedError);
    }
  }

  /**
   * Normalize different error types into a consistent format
   */
  private normalizeError(error: Error | AppError | string, context?: string): ErrorDetails & { type: ErrorType } {
    let normalizedError: ErrorDetails & { type: ErrorType };

    if (error instanceof AppError) {
      normalizedError = {
        type: error.type,
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: error.timestamp,
        context: error.context || context,
        userId: this.getCurrentUserId(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };
    } else if (error instanceof Error) {
      normalizedError = {
        type: this.categorizeError(error),
        message: error.message,
        details: { stack: error.stack },
        timestamp: new Date().toISOString(),
        context,
        userId: this.getCurrentUserId(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };
    } else {
      normalizedError = {
        type: ErrorType.UNKNOWN,
        message: String(error),
        timestamp: new Date().toISOString(),
        context,
        userId: this.getCurrentUserId(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };
    }

    return normalizedError;
  }

  /**
   * Categorize error based on message and type
   */
  private categorizeError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK;
    }
    if (message.includes('validation') || message.includes('required')) {
      return ErrorType.VALIDATION;
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return ErrorType.AUTHENTICATION;
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ErrorType.AUTHORIZATION;
    }
    if (message.includes('upload') || message.includes('file')) {
      return ErrorType.FILE_UPLOAD;
    }
    if (message.includes('submit') || message.includes('form')) {
      return ErrorType.FORM_SUBMISSION;
    }
    if (message.includes('api') || message.includes('server')) {
      return ErrorType.API;
    }
    
    return ErrorType.UNKNOWN;
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: ErrorDetails & { type: ErrorType }, fallback: string): string {
    const messageMap: Record<ErrorType, string> = {
      [ErrorType.NETWORK]: 'Network connection error. Please check your internet connection and try again.',
      [ErrorType.VALIDATION]: 'Please check your input and try again.',
      [ErrorType.AUTHENTICATION]: 'Your session has expired. Please log in again.',
      [ErrorType.AUTHORIZATION]: 'You do not have permission to perform this action.',
      [ErrorType.FILE_UPLOAD]: 'File upload failed. Please try again with a different file.',
      [ErrorType.FORM_SUBMISSION]: 'Form submission failed. Please review your information and try again.',
      [ErrorType.API]: 'Server error. Please try again later.',
      [ErrorType.UNKNOWN]: fallback
    };

    return messageMap[error.type] || fallback;
  }

  /**
   * Show user feedback based on error type
   */
  private showUserFeedback(message: string, type: ErrorType): void {
    // This would integrate with your toast system
    // For now, we'll use a simple console log
    console.warn(`User feedback (${type}):`, message);
    
    // In a real implementation, you might do:
    // toast.error(message);
  }

  /**
   * Add error to queue for batch reporting
   */
  private addToQueue(error: ErrorDetails): void {
    this.errorQueue.push(error);
    
    // Keep queue size manageable
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * Report error to external service
   */
  private async reportError(error: ErrorDetails): Promise<void> {
    try {
      // This would integrate with your error reporting service
      // For now, we'll just log it
      console.log('Reporting error to service:', error);
      
      // In a real implementation, you might do:
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(error)
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Get current user ID (would integrate with your auth system)
   */
  private getCurrentUserId(): string | undefined {
    // This would integrate with your auth context
    return undefined;
  }

  /**
   * Get error queue for debugging
   */
  getErrorQueue(): ErrorDetails[] {
    return [...this.errorQueue];
  }

  /**
   * Clear error queue
   */
  clearErrorQueue(): void {
    this.errorQueue = [];
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Convenience functions
export const handleError = (error: Error | AppError | string, options?: ErrorHandlerOptions) => {
  return errorHandler.handleError(error, options);
};

export const createAppError = (
  message: string,
  type: ErrorType = ErrorType.UNKNOWN,
  code?: string,
  details?: any,
  context?: string
) => {
  return new AppError(message, type, code, details, context);
};
