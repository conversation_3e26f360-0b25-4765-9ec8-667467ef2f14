/**
 * Test utilities for verifying signature fix
 * Use these in browser console to test signature functionality
 */

import { isUploadId, isDataUrl, getSignatureValueType } from './signature-debug';

/**
 * Test signature field values in form context
 */
export function testSignatureInFormContext(formName: string, fieldName: string = 'signature') {
  // Get forms context (this would need to be called from a component with access to context)
  console.log(`Testing signature field: ${formName}.${fieldName}`);
  
  // This is a template - actual implementation would need context access
  console.log('To test manually:');
  console.log('1. Open browser dev tools');
  console.log('2. Go to React DevTools');
  console.log('3. Find FormsContext provider');
  console.log('4. Check forms state');
  console.log(`5. Look for forms.${formName}.${fieldName}`);
  console.log('6. Verify it contains a number (upload ID), not a string (data URL)');
}

/**
 * Test signature field in form state
 */
export function testSignatureInFormState() {
  console.log('To test form state:');
  console.log('1. Open React DevTools');
  console.log('2. Find the form component (e.g., BrokerInformationForm)');
  console.log('3. Look at the form hook state');
  console.log('4. Check watchedValues.signature');
  console.log('5. Verify it contains a number (upload ID), not a data URL');
}

/**
 * Validate signature value
 */
export function validateSignatureValue(value: any, fieldName: string = 'signature'): void {
  console.log(`\n=== Signature Validation: ${fieldName} ===`);
  console.log(`Value: ${value}`);
  console.log(`Type: ${getSignatureValueType(value)}`);
  
  if (isUploadId(value)) {
    console.log('✅ CORRECT: Value is an upload ID');
  } else if (isDataUrl(value)) {
    console.log('❌ ERROR: Value is a data URL (should be upload ID)');
  } else if (value === '' || value === null || value === undefined) {
    console.log('⚠️  WARNING: No signature value found');
  } else {
    console.log('❓ UNKNOWN: Unexpected value type');
  }
}

/**
 * Test signature consistency between form and context
 */
export function testSignatureConsistency(formValue: any, contextValue: any, fieldName: string = 'signature'): void {
  console.log(`\n=== Signature Consistency Test: ${fieldName} ===`);
  console.log(`Form Value: ${formValue} (${getSignatureValueType(formValue)})`);
  console.log(`Context Value: ${contextValue} (${getSignatureValueType(contextValue)})`);
  
  if (formValue === contextValue) {
    if (isUploadId(formValue)) {
      console.log('✅ PERFECT: Values match and are upload IDs');
    } else {
      console.log('⚠️  WARNING: Values match but are not upload IDs');
    }
  } else {
    console.log('❌ ERROR: Values do not match');
    if (isDataUrl(formValue)) {
      console.log('   → Form contains data URL (should be upload ID)');
    }
    if (isDataUrl(contextValue)) {
      console.log('   → Context contains data URL (should be upload ID)');
    }
  }
}

/**
 * Manual test instructions
 */
export function printTestInstructions(): void {
  console.log(`
=== SIGNATURE FIX TESTING INSTRUCTIONS ===

1. SIGNATURE SAVE TEST:
   - Go to any form with signature (e.g., Broker Information)
   - Draw a signature and click save
   - Open browser console
   - Look for "🖋️ Signature Debug:" logs
   - Verify formFieldValue and contextValue are numbers (upload IDs)

2. FORM SUBMISSION TEST:
   - After saving signature, fill out required form fields
   - Submit the form
   - Look for "🖋️ Signature Debug:" logs with action: 'submit'
   - Verify formFieldValue and contextValue are numbers

3. BACKEND VERIFICATION:
   - Open Network tab in dev tools
   - Submit a form with signature
   - Find the PUT request to /onboarding-processes/{id}
   - Check the payload
   - Verify signature field contains a number, not a data URL

4. PERSISTENCE TEST:
   - Save a signature and submit form
   - Refresh the page
   - Verify signature displays correctly
   - Check that form field still contains upload ID

5. ONE-CLICK TEST:
   - Save a signature to user profile first
   - Go to another form with signature
   - Use "Click to Sign and Save" button
   - Verify it sets upload ID, not URL

EXPECTED RESULTS:
- All signature fields should contain numbers (upload IDs)
- No data URLs should be found in form fields or context
- Backend payloads should contain signature: <number>
- Signatures should persist correctly across page reloads

DEBUGGING:
- Use validateSignatureValue(value) to check any signature value
- Use testSignatureConsistency(formVal, contextVal) to compare values
- Check console for "🖋️ Signature Debug:" messages
  `);
}

// Auto-print instructions when imported
if (typeof window !== 'undefined') {
  console.log('Signature test utilities loaded. Run printTestInstructions() for testing guide.');
}
