/**
 * @jest-environment jsdom
 */

import { checkSignature, detectInkAmount, validateSignatureQuality } from '../signature-helper';

// Mock SignatureCanvas
const mockSignatureCanvas = {
  isEmpty: jest.fn(),
  getCanvas: jest.fn(),
  getSignaturePad: jest.fn(),
  _data: []
};

// Mock React ref
const createMockRef = (canvas: any) => ({
  current: canvas
});

describe('signature-helper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkSignature', () => {
    it('should return saved signature when URL is provided', () => {
      const result = checkSignature({ url: 'https://example.com/signature.png' });
      expect(result.isSaved).toBe(true);
      expect(result.data).toEqual({ url: 'https://example.com/signature.png' });
    });

    it('should return unsaved signature when no URL is provided', () => {
      const result = checkSignature({});
      expect(result.isSaved).toBe(false);
      expect(result.data).toBeUndefined();
    });

    it('should return unsaved signature when URL is empty', () => {
      const result = checkSignature({ url: '' });
      expect(result.isSaved).toBe(false);
      expect(result.data).toBeUndefined();
    });
  });

  describe('detectInkAmount', () => {
    it('should return no ink when signature pad is empty', () => {
      const mockCanvas = { ...mockSignatureCanvas, isEmpty: () => true, _data: [] };
      const mockRef = createMockRef(mockCanvas);
      
      const result = detectInkAmount(mockRef as any, 30);
      
      expect(result.isInked).toBe(false);
      expect(result.inkAmount).toBe(0);
      expect(result.quality).toBe('poor');
    });

    it('should detect ink when signature has sufficient data points', () => {
      const mockData = [
        [{ x: 10, y: 10 }, { x: 20, y: 20 }, { x: 30, y: 30 }], // 3 points
        [{ x: 40, y: 40 }, { x: 50, y: 50 }], // 2 points
        [{ x: 60, y: 60 }, { x: 70, y: 70 }, { x: 80, y: 80 }, { x: 90, y: 90 }] // 4 points
      ];

      const mockSignaturePad = { _data: mockData };
      const mockCanvas = {
        ...mockSignatureCanvas,
        isEmpty: () => false,
        getSignaturePad: () => mockSignaturePad
      };
      const mockRef = createMockRef(mockCanvas);

      const result = detectInkAmount(mockRef as any, 5); // Low threshold for testing

      expect(result.isInked).toBe(true);
      expect(result.inkAmount).toBe(9); // Total points: 3 + 2 + 4 = 9
      expect(result.quality).toBe('poor'); // 9 points is poor quality (< 60)
    });

    it('should classify quality correctly based on ink amount', () => {
      // Test poor quality (< 60 points)
      const poorData = [[{ x: 10, y: 10 }]]; // 1 point
      const poorSignaturePad = { _data: poorData };
      const poorCanvas = {
        ...mockSignatureCanvas,
        isEmpty: () => false,
        getSignaturePad: () => poorSignaturePad
      };
      const poorRef = createMockRef(poorCanvas);
      const poorResult = detectInkAmount(poorRef as any, 1);
      expect(poorResult.quality).toBe('poor');
      expect(poorResult.isInked).toBe(true); // Above threshold of 1

      // Test good quality (60-99 points)
      const goodData = Array(70).fill(0).map(() => [{ x: 10, y: 10 }]); // 70 arrays with 1 point each = 70 points
      const goodSignaturePad = { _data: goodData };
      const goodCanvas = {
        ...mockSignatureCanvas,
        isEmpty: () => false,
        getSignaturePad: () => goodSignaturePad
      };
      const goodRef = createMockRef(goodCanvas);
      const goodResult = detectInkAmount(goodRef as any, 1);
      expect(goodResult.quality).toBe('good');
      expect(goodResult.isInked).toBe(true);

      // Test excellent quality (>= 100 points)
      const excellentData = Array(110).fill(0).map(() => [{ x: 10, y: 10 }]); // 110 arrays with 1 point each = 110 points
      const excellentSignaturePad = { _data: excellentData };
      const excellentCanvas = {
        ...mockSignatureCanvas,
        isEmpty: () => false,
        getSignaturePad: () => excellentSignaturePad
      };
      const excellentRef = createMockRef(excellentCanvas);
      const excellentResult = detectInkAmount(excellentRef as any, 1);
      expect(excellentResult.quality).toBe('excellent');
      expect(excellentResult.isInked).toBe(true);
    });
  });

  describe('validateSignatureQuality', () => {
    it('should return invalid for empty signature', () => {
      const mockCanvas = { ...mockSignatureCanvas, isEmpty: () => true };
      const mockRef = createMockRef(mockCanvas);
      
      const result = validateSignatureQuality(mockRef as any, 30);
      
      expect(result.isValid).toBe(false);
      expect(result.hasInk).toBe(false);
      expect(result.hasPixels).toBe(false);
      expect(result.quality).toBe('poor');
      expect(result.errorMessage).toBe('Signature is empty');
    });

    it('should return invalid when ink amount is below threshold', () => {
      const mockData = [[{ x: 10, y: 10 }]]; // Only 1 point
      const mockCanvas = { 
        ...mockSignatureCanvas, 
        isEmpty: () => false, 
        _data: mockData,
        getCanvas: () => ({
          getContext: () => ({
            getImageData: () => ({
              data: new Uint8ClampedArray([255, 255, 255, 255]) // All white pixels
            })
          }),
          width: 500,
          height: 200
        })
      };
      const mockRef = createMockRef(mockCanvas);
      
      const result = validateSignatureQuality(mockRef as any, 30); // High threshold
      
      expect(result.isValid).toBe(false);
      expect(result.hasInk).toBe(false);
      expect(result.errorMessage).toBe('Please add a real signature');
    });
  });
});
