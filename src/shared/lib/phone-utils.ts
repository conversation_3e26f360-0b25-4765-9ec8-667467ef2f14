/**
 * Phone number utility functions
 */

export interface PhoneData {
  masked: string;
  raw: string;
}

/**
 * Extracts raw phone numbers from form data
 * Handles both string and object phone formats
 */
export function getRawPhone(formData: any): Record<string, any> {
  const phoneFields = ['workPhone', 'cellPhone', 'tollfree', 'fax'];
  const rawPhones: Record<string, any> = {};

  phoneFields.forEach(field => {
    const phoneValue = formData[field];
    if (phoneValue) {
      if (typeof phoneValue === 'object' && phoneValue.raw) {
        // Phone is already in object format
        rawPhones[field] = phoneValue;
      } else if (typeof phoneValue === 'string') {
        // Convert string to object format
        rawPhones[field] = {
          masked: phoneValue,
          raw: phoneValue.replace(/\D/g, '')
        };
      }
    }
  });

  return rawPhones;
}

/**
 * Formats a phone number with standard masking
 */
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `1-${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  return phone; // Return original if can't format
}

/**
 * Validates a phone number
 */
export function isValidPhoneNumber(phone: string): boolean {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length >= 10 && cleaned.length <= 11;
}

/**
 * Creates phone data object from string
 */
export function createPhoneData(phone: string): PhoneData {
  const raw = phone.replace(/\D/g, '');
  const masked = formatPhoneNumber(phone);
  
  return { masked, raw };
}
