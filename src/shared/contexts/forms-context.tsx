"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";

import { useAuthContext } from "./auth-context";
import { api } from "@/shared/lib/api";
import { toast } from "@/shared/lib/toast";
import { getCookie, setCookie } from "@/shared/lib/auth";

// Helper function to generate deep population query for onboarding process dynamic zones
const getOnboardingProcessPopulateQuery = () => {
  return `populate[brokerInfo][populate]=*&populate[mpcApplication][populate]=*&populate[contractAndSchedule][populate]=*&populate[paymentAuthorization][populate]=*&populate[policiesAndProcedure][populate]=*&populate[unlicensedInfo][populate]=*&populate[unlicensedPolicies][populate]=*&populate[photos][populate]=*&populate[businessCardInfo][populate]=*&populate[websiteInfo][populate]=*&populate[letterOfDirection][populate]=*&populate[user][populate]=*`;
};
import { generateFormConfigs, calculateCompletionPercentage } from '@/shared/lib/form-navigation';
import { isFormComplete as checkFormComplete } from '@/shared/lib/form-validation';
import type {
  OnboardingForms,
  FormMenuItem,
  FormsContextType,
  FormValidationState,
} from "@/shared/types/forms";

const FormsContext = createContext<FormsContextType | undefined>(undefined);

interface FormsProviderProps {
  children: React.ReactNode;
}

export function FormsProvider({ children }: FormsProviderProps) {
  const { userAuth } = useAuthContext();
  
  const [forms, setForms] = useState<OnboardingForms>({});
  const [menuOrder, setMenuOrder] = useState<FormMenuItem[]>([]);
  const [lastFormVisited, setLastFormVisited] = useState<string | null>(null);
  const [beforeLeave, setBeforeLeave] = useState({
    showAlert: false,
    action: null as string | null,
    route: null as string | null,
  });
  const [onboardingId, setOnboardingId] = useState<string | null>(null);

  // Fetch onboarding process ID and store in cookies
  const fetchOnboardingId = useCallback(async (userId: string): Promise<string | null> => {
    try {
      // First check if we have it in cookies
      const cachedId = getCookie("onboardingId");
      if (cachedId) {
        setOnboardingId(cachedId);
        return cachedId;
      }

      // Fetch from API with deep population for dynamic zones and media fields
      const populateQuery = getOnboardingProcessPopulateQuery();
      const response = await api.get(`/onboarding-processes?filters[user][$eq]=${userId}&${populateQuery}`);
      
      if (response.success && response.data && Array.isArray(response.data) && response.data.length > 0) {
        const onboardingProcess = response.data[0];
        const id = String(onboardingProcess.id);
        
        // Store in cookies for future use
        setCookie("onboardingId", id);
        setOnboardingId(id);
        
        return id;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching onboarding ID:', error);
      return null;
    }
  }, []);

  // Generate dynamic form configuration based on API data and user license status
  const getFormConfig = useCallback(() => {
    const userLicensed = userAuth.userInfo?.licensed === true;
    return generateFormConfigs(forms, userLicensed);
  }, [userAuth.userInfo?.licensed, forms]);

  // Update form data
  const updateForm = useCallback((formName: keyof OnboardingForms, formData: any) => {
    setForms(prev => ({
      ...prev,
      [formName]: {
        ...(prev[formName] as any || {}),
        ...formData,
        lastUpdated: new Date().toISOString(),
      },
      isFormSaved: false,
    }));
  }, []);

  // NEW: Save form data to backend with provided data (preferred method)
  const saveFormData = useCallback(async (formName: keyof OnboardingForms, data: any) => {
    try {
      const userId = userAuth.userInfo?.id;
      if (!userId) {
        throw new Error('No user ID found');
      }

      // Get or fetch onboarding ID
      let currentOnboardingId = onboardingId;
      if (!currentOnboardingId) {
        currentOnboardingId = await fetchOnboardingId(userId);
        if (!currentOnboardingId) {
          throw new Error('No onboarding process found');
        }
      }

      // Update context state first
      updateForm(formName, data);

      // Only send non-empty fields for PUT request
      const filteredFormData = Object.entries(data || {}).reduce((acc, [key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as any);

      // Create the payload with only the changed fields
      const payload = {
        data: {
          [formName]: filteredFormData,
          lastFormVisited: formName,
        }
      };

      const populateQuery = getOnboardingProcessPopulateQuery();
      await api.put(`/onboarding-processes/${currentOnboardingId}?${populateQuery}`, payload);

      setForms(prev => ({
        ...prev,
        isFormSaved: true,
      }));

      toast.success('Form saved successfully');
    } catch (error) {
      console.error('Error saving form:', error);
      toast.error('Failed to save form');
      throw error;
    }
  }, [userAuth.userInfo, onboardingId, fetchOnboardingId, updateForm]);

  // LEGACY: Save form to backend (reads from context - for backward compatibility)
  const saveForm = useCallback(async (formName: keyof OnboardingForms) => {
    const formData = forms[formName];
    await saveFormData(formName, formData);
  }, [forms, saveFormData]);

  // Validate form
  const validateForm = useCallback((formName: keyof OnboardingForms): FormValidationState => {
    const formData = forms[formName];
    if (!formData) {
      return { isValid: false, fields: [] };
    }

    // Basic validation - can be extended
    const fields = Object.entries(formData).map(([field, value]) => ({
      field,
      isValid: value !== null && value !== undefined && value !== '',
      message: !value ? `${field} is required` : undefined,
    }));

    return {
      isValid: fields.every(f => f.isValid),
      fields,
    };
  }, [forms]);

  // Check if form is complete
  const isFormComplete = useCallback((formName: keyof OnboardingForms): boolean => {
    const formData = forms[formName] as any;

    // First check if manually marked as complete
    if (formData?.isFormComplete === true) {
      return true;
    }

    // Otherwise use validation to check completeness
    return checkFormComplete(formName, formData);
  }, [forms]);

  // Calculate completion percentage
  const getFormCompletionPercentage = useCallback((): number => {
    const userLicensed = userAuth.userInfo?.licensed === true;
    return calculateCompletionPercentage(forms, userLicensed);
  }, [forms, userAuth.userInfo?.licensed]);

  // Submit all forms
  const submitAllForms = useCallback(async () => {
    try {
      const userId = userAuth.userInfo?.id;
      if (!userId) {
        throw new Error('No user ID found');
      }

      // Get or fetch onboarding ID
      let currentOnboardingId = onboardingId;
      if (!currentOnboardingId) {
        currentOnboardingId = await fetchOnboardingId(userId);
        if (!currentOnboardingId) {
          throw new Error('No onboarding process found');
        }
      }

      const payload = {
        data: {
          isSubmited: true,
          isLocked: true,
          submissionDate: new Date().toISOString(),
          completionPercent: "100",
        }
      };

      const populateQuery = getOnboardingProcessPopulateQuery();
      await api.put(`/onboarding-processes/${currentOnboardingId}?${populateQuery}`, payload);

      setForms(prev => ({
        ...prev,
        isSubmited: true,
        submissionDate: new Date().toISOString(),
      }));

      toast.success('Onboarding package submitted successfully!');
    } catch (error) {
      console.error('Error submitting forms:', error);
      toast.error('Failed to submit onboarding package');
      throw error;
    }
  }, [userAuth.userInfo, onboardingId, fetchOnboardingId]);

  // Update menu order when forms or user changes
  useEffect(() => {
    const configs = getFormConfig();
    const menuItems = configs.map(config => ({
      key: config.formKey,
      order: config.order.toString(),
      title: config.title,
      slug: config.slug,
    }));
    setMenuOrder(menuItems);
  }, [getFormConfig]);

  // Load forms data on mount
  useEffect(() => {
    const loadFormsData = async () => {
      if (userAuth.userInfo?.id) {
        try {
          // First fetch the onboarding process ID
          const onboardingProcessId = await fetchOnboardingId(userAuth.userInfo.id);
          
          if (onboardingProcessId) {
            // Load onboarding process data using the ID with deep population
            const populateQuery = getOnboardingProcessPopulateQuery();
            const response = await api.get(`/onboarding-processes/${onboardingProcessId}?${populateQuery}`);

            if (response.success && response.data) {
              // Handle nested data structure from Strapi V5
              const responseData = response.data as any;
              const onboardingData = responseData?.data || responseData;



              // Extract forms data from the API response
              const formsData: OnboardingForms = {
                brokerInfo: onboardingData.brokerInfo || {},
                unlicensedInfo: onboardingData.unlicensedInfo || {},
                businessCardInfo: onboardingData.businessCardInfo || {},
                contractAndSchedule: onboardingData.contractAndSchedule || {},
                letterOfDirection: onboardingData.letterOfDirection || {},
                mpcApplication: onboardingData.mpcApplication || {},
                paymentAuthorization: onboardingData.paymentAuthorization || {},
                photos: onboardingData.photos || {},
                policiesAndProcedure: onboardingData.policiesAndProcedure || {},
                unlicensedPolicies: onboardingData.unlicensedPolicies || {},
                websiteInfo: onboardingData.websiteInfo || {},
                isSubmited: onboardingData.isSubmited || false,
                isFormSaved: true,
                completionPercent: onboardingData.completionPercent || "0",
                submissionDate: onboardingData.submissionDate,
              };

              setForms(formsData);

              // Set last form visited if available
              if (onboardingData.lastFormVisited) {
                setLastFormVisited(onboardingData.lastFormVisited);
              }
            }
          } else {
            // Fallback to old method if no onboarding process found
            const populateQuery = getOnboardingProcessPopulateQuery();
            const response = await api.get(`/onboarding-status?filters[user][$eq]=${userAuth.userInfo.id}&${populateQuery}`);

            if (response.success && response.data) {
              const onboardingData = response.data as any;

              // Extract forms data from the API response
              const formsData: OnboardingForms = {
                brokerInfo: onboardingData.brokerInfo || {},
                unlicensedInfo: onboardingData.unlicensedInfo || {},
                businessCardInfo: onboardingData.businessCardInfo || {},
                contractAndSchedule: onboardingData.contractAndSchedule || {},
                letterOfDirection: onboardingData.letterOfDirection || {},
                mpcApplication: onboardingData.mpcApplication || {},
                paymentAuthorization: onboardingData.paymentAuthorization || {},
                photos: onboardingData.photos || {},
                policiesAndProcedure: onboardingData.policiesAndProcedure || {},
                unlicensedPolicies: onboardingData.unlicensedPolicies || {},
                websiteInfo: onboardingData.websiteInfo || {},
                isSubmited: onboardingData.isSubmited || false,
                isFormSaved: true,
                completionPercent: onboardingData.completionPercent || "0",
                submissionDate: onboardingData.submissionDate,
              };

              setForms(formsData);

              // Set last form visited if available
              if (onboardingData.lastFormVisited) {
                setLastFormVisited(onboardingData.lastFormVisited);
              }
            }
          }
        } catch (error) {
          console.error('Error loading forms data:', error);
          // Initialize with empty forms if loading fails
          setForms({});
        }
      }
    };

    loadFormsData();
  }, [userAuth.userInfo?.id, fetchOnboardingId]);

  const contextValue: FormsContextType = {
    forms,
    updateForm,
    saveForm,
    saveFormData, // NEW: Preferred method for saving form data
    validateForm,
    getFormCompletionPercentage,
    isFormComplete,
    submitAllForms,
    menuOrder,
    lastFormVisited,
    setLastFormVisited,
    beforeLeave,
    setBeforeLeave,
    onboardingId,
  };

  return (
    <FormsContext.Provider value={contextValue}>
      {children}
    </FormsContext.Provider>
  );
}

export function useFormsContext() {
  const context = useContext(FormsContext);
  if (context === undefined) {
    throw new Error('useFormsContext must be used within a FormsProvider');
  }
  return context;
}

// Export alias for convenience
export const useForms = useFormsContext;
