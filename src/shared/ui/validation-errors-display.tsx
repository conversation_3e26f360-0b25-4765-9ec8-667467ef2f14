"use client";

import * as React from "react";
import { AlertCircle, X } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { cn } from "@/shared/lib/utils";

interface ValidationError {
  id: string;
  label: string;
  message?: string;
}

interface ValidationErrorsDisplayProps {
  errors: ValidationError[];
  title?: string;
  className?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  showIcon?: boolean;
  variant?: 'default' | 'compact';
}

export function ValidationErrorsDisplay({
  errors,
  title = "The following fields are required:",
  className,
  dismissible = false,
  onDismiss,
  showIcon = true,
  variant = 'default'
}: ValidationErrorsDisplayProps) {
  if (!errors || errors.length === 0) {
    return null;
  }

  const isCompact = variant === 'compact';

  return (
    <div className={cn(
      "bg-red-50 border border-red-200 rounded-md p-4",
      isCompact && "p-3",
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2 flex-1">
          {showIcon && (
            <AlertCircle className={cn(
              "text-red-600 flex-shrink-0 mt-0.5",
              isCompact ? "h-4 w-4" : "h-5 w-5"
            )} />
          )}
          <div className="flex-1">
            <h3 className={cn(
              "font-medium text-red-800 mb-2",
              isCompact ? "text-sm mb-1" : "text-sm"
            )}>
              {title}
            </h3>
            <ul className={cn(
              "list-disc list-inside space-y-1 text-red-600",
              isCompact ? "text-xs" : "text-sm"
            )}>
              {errors.map((error) => (
                <li key={error.id}>
                  {error.message || error.label}
                </li>
              ))}
            </ul>
          </div>
        </div>
        
        {dismissible && onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="text-red-600 hover:text-red-800 hover:bg-red-100 p-1 h-auto"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

interface FormValidationSummaryProps {
  requiredFields: ValidationError[];
  crossFieldErrors: ValidationError[];
  className?: string;
  onDismiss?: () => void;
}

export function FormValidationSummary({
  requiredFields,
  crossFieldErrors,
  className,
  onDismiss
}: FormValidationSummaryProps) {
  const hasErrors = requiredFields.length > 0 || crossFieldErrors.length > 0;

  if (!hasErrors) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {crossFieldErrors.length > 0 && (
        <ValidationErrorsDisplay
          errors={crossFieldErrors}
          title="Form Errors:"
          dismissible={!!onDismiss}
          onDismiss={onDismiss}
        />
      )}
      
      {requiredFields.length > 0 && (
        <ValidationErrorsDisplay
          errors={requiredFields}
          title="Required Fields:"
          dismissible={!!onDismiss}
          onDismiss={onDismiss}
        />
      )}
    </div>
  );
}

interface InlineValidationErrorProps {
  error?: string;
  className?: string;
}

export function InlineValidationError({
  error,
  className
}: InlineValidationErrorProps) {
  if (!error) {
    return null;
  }

  return (
    <div className={cn(
      "flex items-center space-x-1 text-red-600 text-sm mt-1",
      className
    )}>
      <AlertCircle className="h-3 w-3 flex-shrink-0" />
      <span>{error}</span>
    </div>
  );
}
