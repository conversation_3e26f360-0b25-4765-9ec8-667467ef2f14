"use client";

import * as React from "react";
import { CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";
import { Badge } from "@/shared/ui/badge";
import { cn } from "@/shared/lib/utils";

interface FormStatusIndicatorProps {
  isComplete: boolean;
  completionPercentage?: number;
  isLocked?: boolean;
  className?: string;
  showPercentage?: boolean;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function FormStatusIndicator({
  isComplete,
  completionPercentage = 0,
  isLocked = false,
  className,
  showPercentage = false,
  showIcon = true,
  size = 'md'
}: FormStatusIndicatorProps) {
  const getStatusInfo = () => {
    if (isLocked) {
      return {
        label: 'Locked',
        variant: 'secondary' as const,
        icon: AlertCircle,
        color: 'text-gray-600'
      };
    }
    
    if (isComplete) {
      return {
        label: 'Complete',
        variant: 'default' as const,
        icon: CheckCircle,
        color: 'text-green-600'
      };
    }
    
    if (completionPercentage > 0) {
      return {
        label: 'In Progress',
        variant: 'secondary' as const,
        icon: Clock,
        color: 'text-yellow-600'
      };
    }
    
    return {
      label: 'Incomplete',
      variant: 'destructive' as const,
      icon: XCircle,
      color: 'text-red-600'
    };
  };

  const statusInfo = getStatusInfo();
  const Icon = statusInfo.icon;

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          badge: 'text-xs px-2 py-1',
          icon: 'h-3 w-3',
          text: 'text-xs'
        };
      case 'lg':
        return {
          badge: 'text-base px-4 py-2',
          icon: 'h-5 w-5',
          text: 'text-base'
        };
      default:
        return {
          badge: 'text-sm px-3 py-1',
          icon: 'h-4 w-4',
          text: 'text-sm'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Badge 
        variant={statusInfo.variant}
        className={cn("flex items-center space-x-1", sizeClasses.badge)}
      >
        {showIcon && <Icon className={sizeClasses.icon} />}
        <span className="uppercase font-medium">
          {statusInfo.label}
        </span>
      </Badge>
      
      {showPercentage && completionPercentage > 0 && (
        <span className={cn("font-medium", statusInfo.color, sizeClasses.text)}>
          {completionPercentage}%
        </span>
      )}
    </div>
  );
}

interface FormStatusHeaderProps {
  title: string;
  isComplete: boolean;
  completionPercentage?: number;
  isLocked?: boolean;
  className?: string;
}

export function FormStatusHeader({
  title,
  isComplete,
  completionPercentage = 0,
  isLocked = false,
  className
}: FormStatusHeaderProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <h1 className="text-2xl font-bold">{title}</h1>
      <div className="flex items-center space-x-4">
        <span className="text-sm font-medium text-gray-600">Status:</span>
        <FormStatusIndicator
          isComplete={isComplete}
          completionPercentage={completionPercentage}
          isLocked={isLocked}
          showPercentage={true}
          size="md"
        />
      </div>
    </div>
  );
}
