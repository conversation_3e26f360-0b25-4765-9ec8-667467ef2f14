"use client";

import * as React from "react";
import { CheckCircle, XCircle, AlertCircle, Loader2 } from "lucide-react";
import { cn } from "@/shared/lib/utils";

interface ProcessingOverlayProps {
  visible: boolean;
  status: 'loading' | 'success' | 'error' | 'warning' | '';
  message: string;
  className?: string;
  onClose?: () => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export function ProcessingOverlay({
  visible,
  status,
  message,
  className,
  onClose,
  autoClose = false,
  autoCloseDelay = 3000
}: ProcessingOverlayProps) {
  React.useEffect(() => {
    if (visible && autoClose && (status === 'success' || status === 'error')) {
      const timer = setTimeout(() => {
        onClose?.();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [visible, status, autoClose, autoCloseDelay, onClose]);

  if (!visible) return null;

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-6 w-6 animate-spin text-blue-600" />;
      case 'success':
        return (
          <div className="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="h-4 w-4 text-green-600" />
          </div>
        );
      case 'error':
        return (
          <div className="h-6 w-6 bg-red-100 rounded-full flex items-center justify-center">
            <XCircle className="h-4 w-4 text-red-600" />
          </div>
        );
      case 'warning':
        return (
          <div className="h-6 w-6 bg-yellow-100 rounded-full flex items-center justify-center">
            <AlertCircle className="h-4 w-4 text-yellow-600" />
          </div>
        );
      default:
        return <Loader2 className="h-6 w-6 animate-spin text-blue-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'text-green-800';
      case 'error':
        return 'text-red-800';
      case 'warning':
        return 'text-yellow-800';
      default:
        return 'text-gray-800';
    }
  };

  return (
    <div className={cn(
      "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
      className
    )}>
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full mx-4">
        <div className="flex items-center space-x-3">
          {getIcon()}
          <div className="flex-1">
            <p className={cn("text-sm font-medium", getStatusColor())}>
              {message}
            </p>
          </div>
        </div>
        
        {(status === 'success' || status === 'error') && onClose && (
          <div className="mt-4 flex justify-end">
            <button
              onClick={onClose}
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
