"use client";

import * as React from "react";
import { SecureImage } from "@/shared/ui/secure-image";
import { CheckCircle, AlertCircle, Eye, EyeOff } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { cn } from "@/shared/lib/utils";

interface SignaturePreviewProps {
  signatureUrl?: string;
  signatureName?: string;
  signatureDate?: string;
  isValid?: boolean;
  isSaved?: boolean;
  className?: string;
  showToggle?: boolean;
  onTogglePreview?: () => void;
}

/**
 * SignaturePreview component that shows a compact signature preview
 * with validation status and toggle functionality
 */
export function SignaturePreview({
  signatureUrl,
  signatureName,
  signatureDate,
  isValid = false,
  isSaved = false,
  className,
  showToggle = true,
  onTogglePreview
}: SignaturePreviewProps) {
  const [isExpanded, setIsExpanded] = React.useState(false);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
    if (onTogglePreview) {
      onTogglePreview();
    }
  };

  if (!signatureUrl) {
    return null;
  }

  return (
    <div className={cn("border rounded-lg p-4 bg-gray-50", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isSaved && isValid ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-yellow-600" />
          )}
          <span className="text-sm font-medium">
            {isSaved && isValid ? 'Signature Saved' : 'Signature Pending'}
          </span>
        </div>
        
        {showToggle && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleToggle}
            className="h-8 w-8 p-0"
          >
            {isExpanded ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {isExpanded && (
        <div className="mt-4 space-y-2">
          <div className="border border-gray-300 bg-white p-2 inline-block rounded">
            <SecureImage
              src={signatureUrl}
              alt={signatureName || 'Signature Preview'}
              className="w-full h-auto"
              style={{ maxWidth: '300px', height: 'auto' }}
            />
          </div>
          
          <div className="text-xs text-muted-foreground space-y-1">
            {signatureName && (
              <p>Name: {signatureName}</p>
            )}
            {signatureDate && (
              <p>Date: {new Date(signatureDate).toLocaleDateString()}</p>
            )}
            <p>Status: {isSaved && isValid ? 'Valid & Saved' : 'Needs Attention'}</p>
          </div>
        </div>
      )}
    </div>
  );
}
