"use client";

import * as React from "react";
import SignatureCanvas from "react-signature-canvas";
import { Button } from "@/shared/ui/button";
import { Label } from "@/shared/ui/label";
import { cn } from "@/shared/lib/utils";
import {
  hasValidSignature,
  clearSignature,
  loadSignature,
  getSignatureDataURL,
  captureSignature,
  createSignatureFormData,
  detectInkAmount,
  validateSignatureQuality,
  type InkDetectionResult,
  type SignatureQuality
} from "@/shared/lib/signature-helper";

interface SignatureCaptureProps {
  onSignatureChange: (signature: string | null) => void;
  onSignatureSave?: (signature: string) => Promise<void>;
  onInkChange?: (isInked: boolean) => void;
  value?: string;
  label?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  showSaveButton?: boolean;
  minInkThreshold?: number;
  showQualityFeedback?: boolean;
}

export interface SignatureCaptureRef {
  captureSignature: () => Promise<Blob>;
  createFormData: (signatureBlob: Blob, user: any, fieldName?: string) => FormData;
  hasValidSignature: () => boolean;
  clear: () => void;
  saveSignature: () => Promise<void>;
  isSignatureSaved: () => boolean;
}

export const SignatureCapture = React.forwardRef<SignatureCaptureRef, SignatureCaptureProps>(({
  onSignatureChange,
  onSignatureSave,
  onInkChange,
  value,
  label = "Signature",
  required = false,
  className,
  disabled = false,
  showSaveButton = true,
  minInkThreshold = 30,
  showQualityFeedback = false,
}, ref) => {
  const signaturePadRef = React.useRef<SignatureCanvas>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [hasSignature, setHasSignature] = React.useState(false);
  const [isSignatureSaved, setIsSignatureSaved] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);
  const [isInked, setIsInked] = React.useState(false);
  const [inkAmount, setInkAmount] = React.useState(0);
  const [signatureQuality, setSignatureQuality] = React.useState<SignatureQuality>('poor');
  const [errorMessage, setErrorMessage] = React.useState<string>('');
  const [showError, setShowError] = React.useState(false);

  // Legacy-compatible handleSignature function with ink detection
  const handleSignature = React.useCallback(() => {
    if (!signaturePadRef.current) {
      return true; // Return true to indicate empty (legacy behavior)
    }

    // Detect ink amount using legacy method
    const inkResult = detectInkAmount(signaturePadRef as React.RefObject<SignatureCanvas>, minInkThreshold);

    // Update state
    setIsInked(inkResult.isInked);
    setInkAmount(inkResult.inkAmount);
    setSignatureQuality(inkResult.quality);

    // Notify parent component of ink change
    if (onInkChange) {
      onInkChange(inkResult.isInked);
    }

    // Return isEmpty() for legacy compatibility
    return signaturePadRef.current.isEmpty();
  }, [minInkThreshold, onInkChange]);

  const handleClear = () => {
    if (signaturePadRef.current) {
      clearSignature(signaturePadRef as React.RefObject<SignatureCanvas>);
    }
    setHasSignature(false);
    setIsSignatureSaved(false);
    setIsInked(false);
    setInkAmount(0);
    setSignatureQuality('poor');
    setShowError(false);
    setErrorMessage('');
    onSignatureChange(null);
    if (onInkChange) {
      onInkChange(false);
    }
  };

  const handleEnd = React.useCallback(() => {
    if (signaturePadRef.current) {
      // Use the legacy handleSignature function for ink detection
      const isEmpty = handleSignature();

      // Also check pixel validation
      const isValid = hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>);
      setHasSignature(isValid && !isEmpty);

      // Don't auto-save or call onSignatureChange - just update local state
      // The user will explicitly save when they click the Save Signature button
    }
  }, []);

  const handleSaveSignature = async () => {
    if (!signaturePadRef.current) {
      return;
    }

    // Validate signature quality (both ink and pixels)
    const validation = validateSignatureQuality(signaturePadRef as React.RefObject<SignatureCanvas>, minInkThreshold);

    if (!validation.isValid) {
      const errorMsg = validation.errorMessage || 'Please provide a valid signature';
      setErrorMessage(errorMsg);
      setShowError(true);
      console.error('Signature validation failed:', errorMsg);

      // Hide error after 5 seconds
      setTimeout(() => setShowError(false), 5000);
      return;
    }

    // Clear any previous errors
    setShowError(false);
    setErrorMessage('');

    setIsSaving(true);
    try {
      const dataURL = getSignatureDataURL(signaturePadRef as React.RefObject<SignatureCanvas>);
      
      if (!dataURL) {
        throw new Error('Failed to generate signature data URL');
      }

      if (onSignatureSave) {
        await onSignatureSave(dataURL);
        setIsSignatureSaved(true);
        // Also call onSignatureChange to update parent state
        onSignatureChange(dataURL);
      } else {
        // Fallback to just updating the signature
        onSignatureChange(dataURL);
        setIsSignatureSaved(true);
      }
    } catch (error) {
      console.error('Error saving signature:', error);
      setErrorMessage('Failed to save signature');
      setShowError(true);
      // Hide error after 5 seconds
      setTimeout(() => setShowError(false), 5000);
    } finally {
      setIsSaving(false);
    }
  };

  // Expose helper functions for external use
  const captureSignatureBlob = async (): Promise<Blob> => {
    if (signaturePadRef.current && containerRef.current) {
      return captureSignature(signaturePadRef as React.RefObject<SignatureCanvas>, containerRef as React.RefObject<HTMLDivElement>);
    }
    throw new Error('Signature pad not available');
  };

  const createFormData = (signatureBlob: Blob, user: any, fieldName = 'signature'): FormData => {
    return createSignatureFormData(signatureBlob, user, fieldName);
  };

  // Expose methods via ref
  React.useImperativeHandle(ref, () => ({
    captureSignature: captureSignatureBlob,
    createFormData,
    hasValidSignature: () => signaturePadRef.current ? hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>) : false,
    clear: handleClear,
    saveSignature: handleSaveSignature,
    isSignatureSaved: () => isSignatureSaved,
  }));

  // Load existing signature if provided
  React.useEffect(() => {
    if (value && signaturePadRef.current) {
      loadSignature(signaturePadRef as React.RefObject<SignatureCanvas>, value);
      setHasSignature(hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>));
    }
  }, [value]);

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor="signature">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      {/* Signature container matching legacy structure */}
      <div className={cn(
        disabled && "opacity-50 pointer-events-none"
      )}>
        <div
          ref={containerRef}
          style={{
            display: 'table',
            border: '1px solid #ccc',
            width: '500px',
            margin: '0',
            padding: '0',
            background: 'white'
          }}
        >
          <SignatureCanvas
            ref={signaturePadRef}
            canvasProps={{
              width: 500,
              height: 200,
              style: {
                width: '500px',
                height: '200px',
                display: 'block',
                border: 'none',
                background: 'white'
              }
            }}
            onEnd={handleEnd}
            penColor="black"
            backgroundColor="rgb(255, 255, 255)"
          />
        </div>

        <div className="flex justify-between items-center mt-2">
          <div className="flex flex-col">
            <p className="text-sm text-muted-foreground">
              {isSignatureSaved ? "Signature saved" : hasSignature ? "Signature ready to save" : "Please sign above"}
            </p>
            {showQualityFeedback && hasSignature && (
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs text-muted-foreground">
                  Quality:
                </span>
                <span className={cn(
                  "text-xs font-medium",
                  signatureQuality === 'excellent' && "text-green-600",
                  signatureQuality === 'good' && "text-yellow-600",
                  signatureQuality === 'poor' && "text-red-600"
                )}>
                  {signatureQuality}
                </span>
                <span className="text-xs text-muted-foreground">
                  ({inkAmount} points)
                </span>
                {!isInked && (
                  <span className="text-xs text-red-600">
                    - Please add more detail
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Error Message Display */}
          {showError && errorMessage && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600 flex items-center gap-2">
                <span className="text-red-500">⚠</span>
                {errorMessage}
              </p>
            </div>
          )}

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleClear}
              disabled={disabled}
            >
              Clear
            </Button>
            
            {showSaveButton && (
              <Button
                type="button"
                variant="default"
                size="sm"
                onClick={handleSaveSignature}
                disabled={disabled || !hasSignature || isSaving}
              >
                {isSaving ? "Saving..." : "Save Signature"}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

SignatureCapture.displayName = "SignatureCapture";

// Export helper functions for use in forms
export { SignatureCapture as default };
export type { SignatureCaptureProps };
