"use client";

import * as React from "react";
import { HelpCircle, AlertCircle, CheckCircle } from "lucide-react";
import { cn } from "@/shared/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip";

interface FormFieldWithHelpProps {
  children: React.ReactNode;
  label?: string;
  helpText?: string;
  error?: string;
  required?: boolean;
  isValid?: boolean;
  className?: string;
  fieldId?: string;
}

/**
 * Enhanced form field wrapper with help text, validation status, and tooltips
 */
export function FormFieldWithHelp({
  children,
  label,
  helpText,
  error,
  required = false,
  isValid,
  className,
  fieldId
}: FormFieldWithHelpProps) {
  const fieldIdToUse = fieldId || React.useId();

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="flex items-center gap-2">
          <label 
            htmlFor={fieldIdToUse}
            className={cn(
              "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
              error && "text-red-600",
              isValid && "text-green-600"
            )}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
          
          {helpText && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>{helpText}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          
          {/* Validation status indicator */}
          {isValid === true && (
            <CheckCircle className="h-4 w-4 text-green-600" />
          )}
          {error && (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
        </div>
      )}
      
      <div className="relative">
        {React.cloneElement(children as React.ReactElement<any>, {
          id: fieldIdToUse,
          className: cn(
            (children as React.ReactElement<any>).props.className,
            error && "border-red-500 focus:border-red-500 focus:ring-red-500",
            isValid && "border-green-500 focus:border-green-500 focus:ring-green-500"
          ),
          'aria-describedby': helpText ? `${fieldIdToUse}-help` : undefined,
          'aria-invalid': !!error
        })}
      </div>
      
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
      
      {helpText && !error && (
        <p className="text-xs text-muted-foreground">
          {helpText}
        </p>
      )}
    </div>
  );
}

/**
 * Form section with collapsible help content
 */
interface FormSectionWithHelpProps {
  title: string;
  description?: string;
  helpContent?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  defaultExpanded?: boolean;
}

export function FormSectionWithHelp({
  title,
  description,
  helpContent,
  children,
  className,
  defaultExpanded = false
}: FormSectionWithHelpProps) {
  const [isHelpExpanded, setIsHelpExpanded] = React.useState(defaultExpanded);

  return (
    <div className={cn("space-y-4", className)}>
      <div className="border-b pb-2">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">{title}</h3>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          
          {helpContent && (
            <button
              type="button"
              onClick={() => setIsHelpExpanded(!isHelpExpanded)}
              className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
            >
              <HelpCircle className="h-4 w-4" />
              {isHelpExpanded ? 'Hide Help' : 'Show Help'}
            </button>
          )}
        </div>
        
        {helpContent && isHelpExpanded && (
          <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-200">
            <div className="text-sm text-blue-800">
              {helpContent}
            </div>
          </div>
        )}
      </div>
      
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

/**
 * Character counter for text inputs
 */
interface CharacterCounterProps {
  current: number;
  max?: number;
  min?: number;
  className?: string;
}

export function CharacterCounter({ 
  current, 
  max, 
  min, 
  className 
}: CharacterCounterProps) {
  const isOverMax = max && current > max;
  const isUnderMin = min && current < min;
  
  return (
    <div className={cn(
      "text-xs flex justify-between items-center",
      isOverMax && "text-red-600",
      isUnderMin && "text-yellow-600",
      !isOverMax && !isUnderMin && "text-muted-foreground",
      className
    )}>
      <span>
        {min && current < min && `${min - current} more characters needed`}
        {max && current > max && `${current - max} characters over limit`}
      </span>
      <span>
        {current}{max && ` / ${max}`}
      </span>
    </div>
  );
}
