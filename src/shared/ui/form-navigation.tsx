"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight, Save, Send, AlertTriangle } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { cn } from "@/shared/lib/utils";

interface FormNavigationProps {
  onPrevious?: () => void;
  onNext?: () => void;
  onSave?: () => void;
  onSubmit?: () => void;
  canGoBack?: boolean;
  canGoNext?: boolean;
  canSave?: boolean;
  canSubmit?: boolean;
  isLoading?: boolean;
  hasUnsavedChanges?: boolean;
  validationErrors?: number;
  className?: string;
  showValidationSummary?: boolean;
}

/**
 * Form navigation component with save, next/previous, and submit actions
 */
export function FormNavigation({
  onPrevious,
  onNext,
  onSave,
  onSubmit,
  canGoBack = true,
  canGoNext = true,
  canSave = true,
  canSubmit = false,
  isLoading = false,
  hasUnsavedChanges = false,
  validationErrors = 0,
  className,
  showValidationSummary = true
}: FormNavigationProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Validation Summary */}
      {showValidationSummary && validationErrors > 0 && (
        <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <span className="text-sm text-yellow-800">
            {validationErrors} field{validationErrors !== 1 ? 's' : ''} need{validationErrors === 1 ? 's' : ''} attention before you can proceed
          </span>
        </div>
      )}

      {/* Unsaved Changes Warning */}
      {hasUnsavedChanges && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <AlertTriangle className="h-4 w-4 text-blue-600" />
          <span className="text-sm text-blue-800">
            You have unsaved changes. Don't forget to save your progress.
          </span>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          {onPrevious && (
            <Button
              type="button"
              variant="outline"
              onClick={onPrevious}
              disabled={!canGoBack || isLoading}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {onSave && (
            <Button
              type="button"
              variant="outline"
              onClick={onSave}
              disabled={!canSave || isLoading}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isLoading ? 'Saving...' : 'Save Progress'}
            </Button>
          )}

          {onNext && (
            <Button
              type="button"
              onClick={onNext}
              disabled={!canGoNext || isLoading || validationErrors > 0}
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}

          {onSubmit && (
            <Button
              type="submit"
              onClick={onSubmit}
              disabled={!canSubmit || isLoading || validationErrors > 0}
              className="flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              {isLoading ? 'Submitting...' : 'Submit Application'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Form progress indicator
 */
interface FormProgressProps {
  steps: Array<{
    id: string;
    title: string;
    completed: boolean;
    current?: boolean;
  }>;
  className?: string;
}

export function FormProgress({ steps, className }: FormProgressProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2",
                  step.completed && "bg-green-600 border-green-600 text-white",
                  step.current && !step.completed && "border-blue-600 text-blue-600 bg-blue-50",
                  !step.completed && !step.current && "border-gray-300 text-gray-500 bg-white"
                )}
              >
                {step.completed ? '✓' : index + 1}
              </div>
              <span
                className={cn(
                  "mt-2 text-xs text-center max-w-20",
                  step.current && "font-medium text-blue-600",
                  step.completed && "text-green-600",
                  !step.completed && !step.current && "text-gray-500"
                )}
              >
                {step.title}
              </span>
            </div>
            
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "flex-1 h-0.5 mx-2 mt-4",
                  steps[index + 1]?.completed || step.completed
                    ? "bg-green-600"
                    : "bg-gray-300"
                )}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

/**
 * Sticky form actions bar
 */
interface StickyFormActionsProps {
  children: React.ReactNode;
  className?: string;
  show?: boolean;
}

export function StickyFormActions({ 
  children, 
  className, 
  show = true 
}: StickyFormActionsProps) {
  if (!show) return null;

  return (
    <div className={cn(
      "sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-10",
      className
    )}>
      <div className="container mx-auto">
        {children}
      </div>
    </div>
  );
}
