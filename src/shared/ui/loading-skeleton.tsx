"use client";

import * as React from "react";
import { cn } from "@/shared/lib/utils";

interface LoadingSkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  variant?: "text" | "rectangular" | "circular";
  animation?: "pulse" | "wave" | "none";
}

/**
 * LoadingSkeleton component for showing loading states
 */
export function LoadingSkeleton({
  className,
  width = "100%",
  height = "1rem",
  variant = "rectangular",
  animation = "pulse"
}: LoadingSkeletonProps) {
  const baseClasses = "bg-gray-200 dark:bg-gray-700";
  
  const variantClasses = {
    text: "rounded",
    rectangular: "rounded",
    circular: "rounded-full"
  };
  
  const animationClasses = {
    pulse: "animate-pulse",
    wave: "animate-bounce",
    none: ""
  };

  const style = {
    width: typeof width === "number" ? `${width}px` : width,
    height: typeof height === "number" ? `${height}px` : height
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={style}
      role="status"
      aria-label="Loading..."
    />
  );
}

/**
 * FormSkeleton component for form loading states
 */
export function FormSkeleton() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <LoadingSkeleton height="1.5rem" width="30%" />
        <LoadingSkeleton height="2.5rem" />
      </div>
      <div className="space-y-2">
        <LoadingSkeleton height="1.5rem" width="25%" />
        <LoadingSkeleton height="2.5rem" />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <LoadingSkeleton height="1.5rem" width="40%" />
          <LoadingSkeleton height="2.5rem" />
        </div>
        <div className="space-y-2">
          <LoadingSkeleton height="1.5rem" width="35%" />
          <LoadingSkeleton height="2.5rem" />
        </div>
      </div>
      <div className="space-y-2">
        <LoadingSkeleton height="1.5rem" width="20%" />
        <LoadingSkeleton height="6rem" />
      </div>
      <div className="flex justify-end space-x-4">
        <LoadingSkeleton height="2.5rem" width="6rem" />
        <LoadingSkeleton height="2.5rem" width="8rem" />
      </div>
    </div>
  );
}

/**
 * CardSkeleton component for card loading states
 */
export function CardSkeleton() {
  return (
    <div className="border rounded-lg p-6 space-y-4">
      <div className="flex items-center space-x-4">
        <LoadingSkeleton variant="circular" width={40} height={40} />
        <div className="space-y-2 flex-1">
          <LoadingSkeleton height="1.25rem" width="60%" />
          <LoadingSkeleton height="1rem" width="40%" />
        </div>
      </div>
      <LoadingSkeleton height="4rem" />
      <div className="flex justify-between">
        <LoadingSkeleton height="2rem" width="5rem" />
        <LoadingSkeleton height="2rem" width="4rem" />
      </div>
    </div>
  );
}
