"use client";

import * as React from "react";
import { Upload, X, FileText, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Progress } from "@/shared/ui/progress";
import { cn } from "@/shared/lib/utils";
import { toast } from "@/shared/lib/toast";

interface FileUploadProps {
  id?: string;
  name?: string;
  label?: string;
  accept?: string;
  maxSize?: number; // in MB
  required?: boolean;
  disabled?: boolean;
  className?: string;
  value?: File | null;
  onChange?: (file: File | null) => void;
  onUpload?: (file: File) => Promise<any>;
  showProgress?: boolean;
  dragDropText?: string;
  browseText?: string;
  errorMessage?: string;
  allowedTypes?: string[];
}

interface FileUploadState {
  isDragActive: boolean;
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  uploadedFile: any | null;
}

export function FileUpload({
  id = "file-upload",
  name = "file",
  label,
  accept = ".pdf,.doc,.docx,.jpg,.jpeg,.png",
  maxSize = 15, // 15MB default
  required = false,
  disabled = false,
  className,
  value,
  onChange,
  onUpload,
  showProgress = true,
  dragDropText = "Drag/drop your file here or click to choose it.",
  browseText = "Choose File",
  errorMessage,
  allowedTypes = ["pdf", "doc", "docx", "jpg", "jpeg", "png"]
}: FileUploadProps) {
  const [state, setState] = React.useState<FileUploadState>({
    isDragActive: false,
    isUploading: false,
    uploadProgress: 0,
    error: null,
    uploadedFile: null
  });

  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const dropZoneRef = React.useRef<HTMLDivElement>(null);

  // Validate file
  const validateFile = React.useCallback((file: File): string | null => {
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      return `File size exceeds the maximum limit of ${maxSize}MB.`;
    }

    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (fileExtension && !allowedTypes.includes(fileExtension)) {
      return `File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`;
    }

    return null;
  }, [maxSize, allowedTypes]);

  // Handle file selection
  const handleFileSelect = React.useCallback(async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setState(prev => ({ ...prev, error: validationError }));
      toast.error(validationError);
      return;
    }

    setState(prev => ({ ...prev, error: null }));
    onChange?.(file);

    // If onUpload is provided, upload the file
    if (onUpload) {
      setState(prev => ({ ...prev, isUploading: true, uploadProgress: 0 }));
      
      try {
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setState(prev => ({
            ...prev,
            uploadProgress: Math.min(prev.uploadProgress + 10, 90)
          }));
        }, 100);

        const result = await onUpload(file);
        
        clearInterval(progressInterval);
        setState(prev => ({
          ...prev,
          isUploading: false,
          uploadProgress: 100,
          uploadedFile: result
        }));
        
        toast.success("File uploaded successfully!");
      } catch (error) {
        setState(prev => ({
          ...prev,
          isUploading: false,
          uploadProgress: 0,
          error: error instanceof Error ? error.message : "Upload failed"
        }));
        toast.error("Failed to upload file");
      }
    }
  }, [validateFile, onChange, onUpload]);

  // Handle file input change
  const handleFileInputChange = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  // Handle drag events
  const handleDragOver = React.useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setState(prev => ({ ...prev, isDragActive: true }));
    }
  }, [disabled]);

  const handleDragLeave = React.useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setState(prev => ({ ...prev, isDragActive: false }));
  }, []);

  const handleDrop = React.useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setState(prev => ({ ...prev, isDragActive: false }));
    
    if (disabled) return;

    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [disabled, handleFileSelect]);

  // Handle file removal
  const handleRemoveFile = React.useCallback(() => {
    onChange?.(null);
    setState(prev => ({
      ...prev,
      error: null,
      uploadedFile: null,
      uploadProgress: 0
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onChange]);

  // Handle browse button click
  const handleBrowseClick = React.useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  const hasFile = value || state.uploadedFile;
  const showError = state.error || errorMessage;

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div
        ref={dropZoneRef}
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          state.isDragActive && !disabled
            ? "border-blue-400 bg-blue-50"
            : "border-gray-300",
          disabled && "opacity-50 cursor-not-allowed",
          showError && "border-red-300 bg-red-50"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Input
          ref={fileInputRef}
          id={id}
          name={name}
          type="file"
          accept={accept}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        {hasFile ? (
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <FileText className="h-8 w-8 text-blue-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">
                  {value?.name || state.uploadedFile?.name || "File selected"}
                </p>
                <p className="text-sm text-gray-500">
                  {value ? `${(value.size / (1024 * 1024)).toFixed(2)} MB` : "Uploaded"}
                </p>
              </div>
            </div>
            
            {state.isUploading && showProgress && (
              <div className="space-y-2">
                <Progress value={state.uploadProgress} className="w-full" />
                <p className="text-sm text-gray-600">Uploading... {state.uploadProgress}%</p>
              </div>
            )}
            
            {!state.isUploading && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleRemoveFile}
                disabled={disabled}
              >
                <X className="h-4 w-4 mr-2" />
                Remove File
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-900">{dragDropText}</p>
              <p className="text-xs text-gray-500 mt-1">
                Max file size: {maxSize}MB. Allowed types: {allowedTypes.join(', ')}
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={handleBrowseClick}
              disabled={disabled}
            >
              {browseText}
            </Button>
          </div>
        )}
      </div>

      {showError && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertCircle className="h-4 w-4" />
          <span>{showError}</span>
        </div>
      )}

      {state.uploadedFile && !state.isUploading && (
        <div className="flex items-center space-x-2 text-green-600 text-sm">
          <CheckCircle className="h-4 w-4" />
          <span>File uploaded successfully</span>
        </div>
      )}
    </div>
  );
}
