"use client";

import * as React from "react";
import { UseFormReturn, FieldValues } from "react-hook-form";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { populateWithUserData } from "./use-base-form";
import type { OnboardingForms } from "@/shared/types/forms";

export interface FormDataPopulationOptions {
  userFieldMappings?: Record<string, string>;
  skipUserPopulation?: boolean;
  customPopulationLogic?: (formData: any, userData: any, form: UseFormReturn<any>) => void;
}

/**
 * Enhanced hook for handling conditional form data population
 * Based on firstSaveComplete status:
 * - firstSaveComplete: false -> populate with user data
 * - firstSaveComplete: true -> populate with saved form data
 */
export function useFormDataPopulation<T extends FieldValues>(
  formName: keyof OnboardingForms,
  form: UseFormReturn<T>,
  options: FormDataPopulationOptions = {}
) {
  const { forms } = useFormsContext();
  const { userAuth } = useAuthContext();
  const { userFieldMappings, skipUserPopulation, customPopulationLogic } = options;
  
  // Track if we've already populated to prevent multiple attempts
  const [hasPopulated, setHasPopulated] = React.useState(false);
  const [lastUserId, setLastUserId] = React.useState<string | null>(null);
  const [lastFormDataState, setLastFormDataState] = React.useState<boolean | null>(null);

  React.useEffect(() => {
    const formData = forms[formName] as any;
    const userData = userAuth.userInfo;
    const currentUserId = userData?.id;
    const currentFirstSaveComplete = formData?.firstSaveComplete;
    
    if (!userData) {
      console.log(`🔄 ${formName}: No user data available, skipping population`);
      return;
    }

    // Reset population state if user changed or firstSaveComplete status changed
    if (lastUserId !== currentUserId || lastFormDataState !== currentFirstSaveComplete) {
      setHasPopulated(false);
      setLastUserId(currentUserId || null);
      setLastFormDataState(currentFirstSaveComplete);
    }

    // Prevent multiple population attempts for the same state
    if (hasPopulated) {
      console.log(`🔄 ${formName}: Already populated for current state, skipping`);
      return;
    }

    // CRITICAL FIX: Check firstSaveComplete to determine population source
    if (!formData?.firstSaveComplete) {
      // First time: populate with user data
      console.log(`🔄 ${formName}: First save not complete, populating with user data`);
      
      if (!skipUserPopulation && userFieldMappings) {
        populateWithUserData(form, userAuth, userFieldMappings);
        console.log(`🔄 ${formName}: Applied user field mappings:`, userFieldMappings);
      }
      
      // Apply custom population logic if provided (for special cases like photos)
      if (customPopulationLogic) {
        customPopulationLogic(formData, userData, form);
        console.log(`🔄 ${formName}: Applied custom population logic`);
      }
      
      setHasPopulated(true);
    } 
    else if (formData?.firstSaveComplete && formData) {
      // Subsequent times: populate with saved form data
      console.log(`🔄 ${formName}: First save complete, using saved form data`);
      
      // Reset form with saved data, preserving React Hook Form state
      const formFields = Object.keys(form.getValues());
      let populatedFields = 0;
      
      formFields.forEach(field => {
        if (formData[field] !== undefined && formData[field] !== null) {
          form.setValue(field as any, formData[field]);
          populatedFields++;
        }
      });
      
      console.log(`🔄 ${formName}: Populated ${populatedFields} fields from saved data`);
      setHasPopulated(true);
    } else {
      console.log(`🔄 ${formName}: No form data available yet`);
    }
  }, [
    (forms[formName] as any)?.firstSaveComplete, 
    userAuth.userInfo?.id, 
    formName, 
    // Remove form from deps to prevent infinite loops
    // userFieldMappings, skipUserPopulation, customPopulationLogic are stable
  ]);

  // Return information about current population status for debugging
  return {
    isFirstSave: !(forms[formName] as any)?.firstSaveComplete,
    hasFormData: !!forms[formName],
    hasUserData: !!userAuth.userInfo,
  };
}

/**
 * Utility function to create user field mappings for common patterns
 */
export function createUserFieldMappings(fields: Array<string | { formField: string; userField: string }>) {
  const mappings: Record<string, string> = {};
  
  fields.forEach(field => {
    if (typeof field === 'string') {
      // Simple mapping: form field name = user field name
      mappings[field] = field;
    } else {
      // Custom mapping: form field -> user field
      mappings[field.formField] = field.userField;
    }
  });
  
  return mappings;
}

/**
 * Common user field mappings for reuse across forms
 */
export const commonUserFieldMappings = {
  personal: createUserFieldMappings([
    'firstname',
    'middlename', 
    'lastname',
    'workEmail',
    'cellPhone',
    'workPhone',
  ]),
  
  address: createUserFieldMappings([
    'address',
    'city',
    'province',
    'postalCode',
  ]),
  
  professional: createUserFieldMappings([
    'position',
    'license',
    'bio',
  ]),
  
  social: createUserFieldMappings([
    'facebook',
    'instagram',
    'linkedin',
    'twitter',
    'youtube',
  ]),
};
