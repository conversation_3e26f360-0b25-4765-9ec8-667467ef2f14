"use client";

import * as React from "react";
import { useForm, UseFormProps, FieldValues, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";
import type { OnboardingForms } from "@/shared/types/forms";
import { z } from "zod";

export interface ProcessingStatus {
  visible: boolean;
  status: 'loading' | 'success' | 'error' | '';
  message: string;
}

export interface BaseFormHookOptions<T extends FieldValues> {
  formName: keyof OnboardingForms;
  schema: z.ZodSchema<T>;
  defaultValues?: T | (() => T);
  mode?: UseFormProps<T>['mode'];
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  transformSubmitData?: (data: T) => any;
}

export interface BaseFormHookReturn<T extends FieldValues> {
  form: UseFormReturn<T>;
  isLoading: boolean;
  processingStatus: ProcessingStatus;
  setProcessingStatus: React.Dispatch<React.SetStateAction<ProcessingStatus>>;
  handleSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
  onSubmit: (data: T) => Promise<void>;
  watchedValues: T;
  errors: UseFormReturn<T>['formState']['errors'];
}

/**
 * Base form hook that provides standardized form handling patterns
 * Eliminates dual state management by using RHF as single source of truth
 */
export function useBaseForm<T extends FieldValues>({
  formName,
  schema,
  defaultValues,
  mode = "onChange",
  onSuccess,
  onError,
  transformSubmitData,
}: BaseFormHookOptions<T>): BaseFormHookReturn<T> {
  const { forms, saveFormData } = useFormsContext();
  const { userAuth } = useAuthContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [processingStatus, setProcessingStatus] = React.useState<ProcessingStatus>({
    visible: false,
    status: '',
    message: ''
  });

  // Get default values from context or provided defaults
  const getDefaultValues = React.useCallback((): T => {
    const contextData = forms[formName];
    
    if (typeof defaultValues === 'function') {
      const computed = defaultValues();
      return (contextData && typeof contextData === 'object') ? { ...computed, ...contextData } : computed;
    }

    if (defaultValues) {
      return (contextData && typeof contextData === 'object') ? { ...defaultValues, ...contextData } : defaultValues;
    }

    return (contextData || {}) as T;
  }, [forms, formName, defaultValues]);

  // Initialize form with proper default values
  const form = useForm<T>({
    resolver: zodResolver(schema as any),
    mode,
    defaultValues: getDefaultValues() as any,
  });

  const { watch, formState: { errors } } = form;
  const watchedValues = watch();

  // Reset form when context data changes (for cross-form updates)
  React.useEffect(() => {
    const contextData = forms[formName];
    if (contextData) {
      const newDefaults = getDefaultValues();
      form.reset(newDefaults);
    }
  }, [forms[formName], form, getDefaultValues]);

  // Handle form submission with standardized pattern
  const onSubmit = React.useCallback(async (data: T) => {
    setIsLoading(true);
    setProcessingStatus({ 
      visible: true, 
      status: 'loading', 
      message: `Saving ${formName}...` 
    });

    try {
      // CRITICAL FIX: Get current form data to check firstSaveComplete status
      const currentFormData = forms[formName] as any;
      const isFirstSave = !currentFormData?.firstSaveComplete;
      
      // Transform data if transformer provided
      const submitData = transformSubmitData ? transformSubmitData(data) : data;
      
      // Add standard metadata - FIXED: Properly handle firstSaveComplete
      const formDataToSubmit = {
        ...submitData,
        isFormComplete: true,
        firstSaveComplete: true, // Set to true on ANY save (this indicates form has been saved)
        lastUpdated: new Date().toISOString(),
        // Add metadata for debugging and tracking
        wasFirstSave: isFirstSave,
      };

      // Save using new context method
      await saveFormData(formName, formDataToSubmit);

      // Update form state with saved data
      form.reset(formDataToSubmit);

      setProcessingStatus({ 
        visible: true, 
        status: 'success', 
        message: 'Form saved successfully!' 
      });

      toast.success(`${formName} saved successfully!`);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(data);
      }

      // Hide success message after delay
      setTimeout(() => {
        setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);

    } catch (error) {
      console.error(`Error saving ${formName}:`, error);
      
      const errorMessage = error instanceof Error ? error.message : `Failed to save ${formName}`;
      
      setProcessingStatus({ 
        visible: true, 
        status: 'error', 
        message: errorMessage 
      });

      toast.error(errorMessage);

      // Call error callback if provided
      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }

      // Hide error message after delay
      setTimeout(() => {
        setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);

    } finally {
      setIsLoading(false);
    }
  }, [formName, saveFormData, form, transformSubmitData, onSuccess, onError]);

  // Wrapped handleSubmit for easy use
  const handleSubmit = form.handleSubmit(onSubmit);

  return {
    form,
    isLoading,
    processingStatus,
    setProcessingStatus,
    handleSubmit,
    onSubmit,
    watchedValues,
    errors,
  };
}

/**
 * Helper function to create form-specific hooks using the base pattern
 */
export function createFormHook<T extends FieldValues>(
  formName: keyof OnboardingForms,
  schema: z.ZodSchema<T>,
  defaultValues?: T | (() => T)
) {
  return function useFormHook(options?: Partial<BaseFormHookOptions<T>>) {
    return useBaseForm<T>({
      formName,
      schema,
      defaultValues,
      ...options,
    });
  };
}

/**
 * Utility function to populate form with user data
 * Helps with the common pattern of pre-filling forms with user profile data
 */
export function populateWithUserData<T extends FieldValues>(
  form: UseFormReturn<T>,
  userAuth: any,
  fieldMappings: Record<string, string>
) {
  if (!userAuth?.userInfo) return;

  const user = userAuth.userInfo;
  
  Object.entries(fieldMappings).forEach(([formField, userField]) => {
    const value = user[userField];
    if (value && typeof value === 'string' && value.trim() !== '') {
      form.setValue(formField as any, value as any);
    }
  });
}
