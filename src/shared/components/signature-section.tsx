"use client";

import * as React from "react";
import { SignatureCapture } from "@/shared/ui/signature-capture";
import { OneClickSignature } from "@/shared/components/one-click-signature";
import { SavedSignatureDisplay } from "@/shared/components/saved-signature-display";
import { saveSignatureToBackend, checkSignature } from "@/shared/lib/signature-helper";
import { api } from "@/shared/lib/api";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { toast } from "@/shared/lib/toast";
import { logSignatureDebug } from "@/shared/lib/signature-debug";
import type { OnboardingForms } from "@/shared/types/forms";

interface SignatureSectionProps {
  value?: string | number | any;
  onSignatureChange: (signature: string | number | null) => void;
  setValue: (field: any, value: any) => void;
  form?: any; // Form instance for getting current values
  fieldName?: string;
  formName?: keyof OnboardingForms; // Add formName prop for saving to form data
  label?: string;
  required?: boolean;
  className?: string;
  allowOneClick?: boolean;
  showProfileSignature?: boolean;
  showQualityFeedback?: boolean;
  minInkThreshold?: number;
}

export function SignatureSection({
  value,
  onSignatureChange,
  setValue,
  form,
  fieldName = 'signature',
  formName,
  label = "Signature",
  required = true,
  className,
  allowOneClick = true,
  showProfileSignature = true,
  showQualityFeedback = false,
  minInkThreshold = 30
}: SignatureSectionProps) {
  const { userAuth } = useAuthContext();
  const { saveFormData } = useFormsContext(); // Use new method to avoid race conditions
  const [signature, setSignature] = React.useState<{ isSaved: boolean; data?: any }>({ isSaved: false, data: null });
  const [isSavingSignature, setIsSavingSignature] = React.useState(false);

  const [errorMessage, setErrorMessage] = React.useState<string>('');
  const [showError, setShowError] = React.useState(false);

  // Handle signature saving
  const handleSignatureSave = async (signatureDataUrl: string) => {
    setIsSavingSignature(true);
    try {
      const user = {
        firstname: userAuth?.userInfo?.firstname || '',
        lastname: userAuth?.userInfo?.lastname || '',
        id: userAuth?.userInfo?.id || ''
      };

      // Save signature to backend (S3 + user profile) with form name
      const signatureData = await saveSignatureToBackend(signatureDataUrl, user, api, fieldName, formName);

      // Update local state
      setSignature({ isSaved: true, data: signatureData });

      // CRITICAL FIX: Set the form field to the upload ID, not the data URL
      // This ensures form submission sends the correct signature reference
      setValue(fieldName, signatureData.id);
      onSignatureChange(signatureData.id);

      // Debug logging
      logSignatureDebug({
        formName: formName || 'unknown',
        fieldName,
        formFieldValue: signatureData.id,
        contextValue: signatureData.id,
        uploadId: signatureData.id,
        action: 'save'
      });

      // CRITICAL FIX: Save entire form with pre-populated data and firstSaveComplete: true
      // Use new saveFormData method to eliminate race condition
      if (formName && saveFormData) {
        // Get current form values from the form hook
        const currentFormValues = form?.getValues() || {};

        // Prepare complete form data with signature
        const formDataToSave = {
          ...currentFormValues,
          [fieldName]: signatureData.id, // Send the upload ID
          signatureDate: new Date().toISOString(),
          firstSaveComplete: true,
          lastUpdated: new Date().toISOString(),
        };

        // Save complete form data in single operation (eliminates race condition)
        await saveFormData(formName, formDataToSave);

        console.log('🖋️ Signature saved and form updated with firstSaveComplete: true');
      }

      toast.success('Signature saved successfully');
      setShowError(false);
      setErrorMessage('');
    } catch (error) {
      console.error('Error saving signature:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to save signature';
      setErrorMessage(errorMsg);
      setShowError(true);
      toast.error(errorMsg);

      // Hide error after 10 seconds
      setTimeout(() => setShowError(false), 10000);
    } finally {
      setIsSavingSignature(false);
    }
  };

  // Handle one-click signing using existing user signature or initials
  const handleOneClickSign = async () => {
    // Determine if this is for initials or signature
    const isInitials = fieldName.toLowerCase().includes('initial');
    const existingData = isInitials
      ? userAuth?.userInfo?.initials
      : userAuth?.userInfo?.signature;

    if (!existingData?.url) {
      toast.error(`No existing ${isInitials ? 'initials' : 'signature'} found`);
      return;
    }

    setIsSavingSignature(true);
    try {
      // Update form state with existing signature/initials
      setSignature({ isSaved: true, data: existingData });

      // CRITICAL FIX: Set the form field to the upload ID, not the URL
      // This ensures form submission sends the correct signature reference
      setValue(fieldName, existingData.id);
      onSignatureChange(existingData.id);

      // Debug logging
      logSignatureDebug({
        formName: formName || 'unknown',
        fieldName,
        formFieldValue: existingData.id,
        contextValue: existingData.id,
        uploadId: typeof existingData.id === 'number' ? existingData.id : undefined,
        action: 'save'
      });

      console.log('FORM NAME:', formName);
      console.log('FIELD NAME:', fieldName);
      console.log('EXISTING DATA:', existingData);
     

      // CRITICAL FIX: Save entire form with pre-populated data and firstSaveComplete: true
      // Use new saveFormData method to eliminate race condition
      if (formName && saveFormData) {
        // Get current form values from the form hook (includes pre-populated user data)
        const currentFormValues = form?.getValues() || {};

        // Prepare complete form data with signature
        const formDataToSave = {
          ...currentFormValues,
          [fieldName]: existingData.id, // Send the upload ID
          signatureDate: new Date().toISOString(),
          firstSaveComplete: true,
          lastUpdated: new Date().toISOString(),
        };

        console.log('🖋️ One-click signature: Saving complete form data:', formDataToSave);

        // Save complete form data in single operation (eliminates race condition)
        await saveFormData(formName, formDataToSave);

        console.log('🖋️ One-click signature saved and form updated with firstSaveComplete: true');
      }

      toast.success(`${isInitials ? 'Initials' : 'Signature'} applied successfully`);
      setShowError(false);
      setErrorMessage('');
    } catch (error) {
      console.error('Error with one-click signing:', error);
      const errorMsg = error instanceof Error ? error.message : `Failed to apply ${isInitials ? 'initials' : 'signature'}`;
      setErrorMessage(errorMsg);
      setShowError(true);
      toast.error(errorMsg);

      // Hide error after 10 seconds
      setTimeout(() => setShowError(false), 10000);
    } finally {
      setIsSavingSignature(false);
    }
  };



  // Check if signature exists on component mount and fetch signature data if needed
  React.useEffect(() => {
    const checkExistingSignature = async () => {
      if (!value) {
        setSignature({ isSaved: false, data: null });
        return;
      }

      // If value is a number (upload ID), fetch the signature data from backend
      if (typeof value === 'number' || (typeof value === 'string' && /^\d+$/.test(value))) {
        try {
          const uploadId = typeof value === 'string' ? parseInt(value) : value;
          const response = await api.get(`/upload/files/${uploadId}`);

          if (response.success && response.data) {
            const uploadData = response.data as any;
            const signatureData = {
              id: uploadId,
              url: uploadData.url,
              name: uploadData.name || `${fieldName}-${userAuth?.userInfo?.firstname || 'user'}-${Date.now()}`,
              createdAt: uploadData.createdAt || new Date().toISOString()
            };
            setSignature({ isSaved: true, data: signatureData });
            console.log('🖋️ Loaded existing signature from upload ID:', uploadId, signatureData);
          } else {
            console.warn('🖋️ Failed to load signature data for upload ID:', uploadId);
            setSignature({ isSaved: false, data: null });
          }
        } catch (error) {
          console.error('🖋️ Error fetching signature data:', error);
          setSignature({ isSaved: false, data: null });
        }
      }
      // If value is a URL string, treat it as legacy signature data
      else if (typeof value === 'string' && (value.startsWith('http') || value.startsWith('data:'))) {
        const signatureCheck = checkSignature({ url: value });
        setSignature(signatureCheck);
        console.log('🖋️ Loaded legacy signature from URL:', value);
      }
      // If value is an object with url property, use it directly
      else if (typeof value === 'object' && value !== null && (value as any)?.url) {
        const signatureCheck = checkSignature(value);
        setSignature(signatureCheck);
        console.log('🖋️ Loaded signature from object:', value);
      }
      else {
        console.log('🖋️ Unknown signature value type:', typeof value, value);
        setSignature({ isSaved: false, data: null });
      }
    };

    checkExistingSignature();
  }, [value, fieldName, userAuth?.userInfo?.firstname]);

  // Determine what to show based on signature status and user profile
  const renderSignatureComponent = () => {
    // Debug logging
    console.log('🖋️ SignatureSection Debug:', {
      fieldName,
      formName,
      value,
      signatureIsSaved: signature.isSaved,
      signatureData: signature.data,
      allowOneClick,
      showProfileSignature,
      userSignature: userAuth?.userInfo?.signature,
      userInitials: userAuth?.userInfo?.initials,
      userAuthInitialized: userAuth?.initialized,
      userInfo: userAuth?.userInfo ? 'loaded' : 'not loaded'
    });

    // If signature is already saved for this form, show saved signature display
    if (signature.isSaved && signature.data?.url) {
      return (
        <SavedSignatureDisplay
          signatureUrl={signature.data.url}
          signatureName={signature.data.name}
          signatureDate={signature.data.createdAt}
        />
      );
    }

    // If user has a profile signature/initials and one-click is enabled, show one-click option
    const isInitials = fieldName.toLowerCase().includes('initial');
    const existingData = isInitials
      ? userAuth?.userInfo?.initials
      : userAuth?.userInfo?.signature;

    if (
      allowOneClick &&
      showProfileSignature &&
      existingData?.url &&
      !signature.isSaved
    ) {
      return (
        <OneClickSignature
          signatureUrl={existingData.url}
          signatureName={existingData.name}
          onOneClickSign={handleOneClickSign}
          disabled={isSavingSignature}
        />
      );
    }

    // Default: show signature canvas
    return (
      <SignatureCapture
        value={value}
        onSignatureChange={onSignatureChange}
        onSignatureSave={handleSignatureSave}
        required={required}
        showSaveButton={true}
        label={label}
        showQualityFeedback={showQualityFeedback}
        minInkThreshold={minInkThreshold}
      />
    );
  };

  return (
    <div className={className}>
      {renderSignatureComponent()}

      {/* Error Message Display */}
      {showError && errorMessage && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600 flex items-center gap-2">
            <span className="text-red-500 font-bold">⚠</span>
            {errorMessage}
          </p>
        </div>
      )}

      {/* Signature status message */}
      {!signature.isSaved && !showError && (
        <div className="mt-2">
          <p className="text-sm text-muted-foreground">
            Please save your signature first.
          </p>
        </div>
      )}
    </div>
  );
}

// Export helper to check if signature is saved
export function useSignatureStatus(value?: string) {
  const [signature, setSignature] = React.useState<{ isSaved: boolean; data?: any }>({ isSaved: false, data: null });

  React.useEffect(() => {
    if (value) {
      const signatureCheck = checkSignature({ url: value });
      setSignature(signatureCheck);
    }
  }, [value]);

  return signature;
}
