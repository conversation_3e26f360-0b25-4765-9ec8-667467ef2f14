{"data": {"id": 1044, "slug": null, "completionPercent": null, "isSubmited": false, "lastFormVisited": "brokerInfo", "isLocked": false, "isComplete": false, "browserInfo": null, "submissionDate": null, "createdAt": "2025-09-08T21:01:30.864Z", "updatedAt": "2025-09-23T18:52:17.105Z", "publishedAt": "2025-09-23T18:52:17.246Z", "documentId": "kpoxfk2tymdfag4u2t2tgb0y", "locale": null, "user": {"id": 441, "username": "brunoOnboardingTest", "email": "<EMAIL>", "provider": "local", "password": "$2a$10$LiGIcZyI5ewRqxNf2mHilexPjtgzall4kEiCrxa/fLE20yYnDNCbm", "confirmed": true, "blocked": false, "confirmationToken": null, "resetPasswordToken": null, "license": null, "phone": "**********", "bio": "test", "address": "223 14 Street NW ", "firstname": "<PERSON> ", "lastname": "Sousa", "position": "Test", "bioTitle": null, "applicationLink": null, "brokerage": null, "mapEmbedSrc": null, "facebook": "https://facebook.com/hellobrunao", "instagram": "https://instagram.com/hellobrunao", "twitter": null, "linkedin": null, "youtube": null, "whatsapp": null, "hasLogo2": null, "fax": null, "hasCustomBanner": false, "titles": null, "photoOnPrintable": null, "website": null, "ext": null, "qrCodes": null, "qrCodeOnPrintable": null, "isOnboarding": true, "homePhone": null, "cellPhone": "**********", "emergencyContact": "Test", "emergencyPhone": "**********", "birthdate": "2025-05-29", "sin": "999888777", "startDate": null, "dietRestriction": null, "additionalNotes": null, "middlename": "Cesar", "city": "Calgary", "postalCode": "T2N 1Z6", "tollfree": null, "workEmail": "<EMAIL>", "websiteOptIn": true, "ownDomain": true, "providedDomain": false, "websiteDomainName": "brunao.me", "websiteDomainRegistrar": "Spaceship", "tollfreeExt": null, "gender": null, "appointmentScheduleLink": null, "province": "AB", "tshirtSize": "XS", "additionalDomainNames": null, "secondaryWebsite": null, "onboardingStartDate": null, "onboardingEndDate": null, "loginCount": "4", "legalName": null, "preferredName": null, "googleTag": null, "facebookPixelTag": null, "googleWebsiteVerification": null, "googleTagManagerInHead": null, "googleTagManagerInBody": null, "thirdPartyScriptTag": null, "emptyPrintableFooter": false, "googleReviewsLink": null, "facebookHandler": "<PERSON><PERSON><PERSON><PERSON>", "instagramHandler": "<PERSON><PERSON><PERSON><PERSON>", "linkedinHandler": null, "twitterHandler": null, "youtubeHandler": null, "notListed": true, "personalAddress": "Test", "personalCity": "Test", "personalProvince": "AB", "personalPostalCode": "TESTS", "personalSuiteUnit": "9999", "suiteUnit": null, "licensed": true, "chatWidgetCode": null, "reviewWidgetCode": null, "emailNotifications": true, "circularPhotoUrl": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "isStaffMember": false, "tiktok": null, "tiktokHandler": null, "pinterest": null, "pinterestHandler": null, "threads": null, "threadsHandler": null, "bluesky": null, "blueskyHandler": null, "websiteGitBranch": null, "isComplianceStaff": null, "createdAt": "2025-05-01T20:08:57.840Z", "updatedAt": "2025-09-23T18:49:10.460Z", "documentId": "mvw9pfvbnwuwo0n7mu54cdog", "locale": null, "publishedAt": "2025-09-16T18:11:41.239Z", "role": {"id": 1, "name": "Authenticated", "description": "Default role given to authenticated user.", "type": "authenticated", "createdAt": null, "updatedAt": "2025-09-23T15:28:32.339Z", "documentId": "diwtlvst8voyvj4hlnndiycb", "locale": null, "publishedAt": "2025-08-11T18:59:08.635Z"}, "photo": {"id": 14006, "name": "Bruno Portrait-1 low.jpg", "alternativeText": "", "caption": "", "width": null, "height": null, "formats": {"small": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/1746217608669_sebvo-<PERSON>_Portrait-1_low.jpg", "hash": "1746217608669_sebvo_small", "mime": "image/jpeg", "name": "1746217608669_sebvo_small.jpg", "size": 8620, "width": 300}, "medium": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/1746217608669_sebvo-<PERSON>_Portrait-1_low.jpg", "hash": "1746217608669_sebvo_medium", "mime": "image/jpeg", "name": "1746217608669_sebvo_medium.jpg", "size": 33591, "width": 750}, "squared": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1746217608669_sebvo-<PERSON>_Portrait-1_low.jpg", "hash": "1746217608669_sebvo_squared", "mime": "image/jpeg", "name": "1746217608669_sebvo_squared.jpg", "size": 8620, "width": 300, "height": 300}}, "hash": "1746217608669_sebvo", "ext": ".jpg", "mime": "image/jpeg", "size": 34200, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1746217608669_sebvo-<PERSON>_Portrait-1_low.jpg", "previewUrl": null, "provider": "aws-s3-resizing-and-optimisation", "provider_metadata": null, "createdAt": "2025-05-02T20:26:49.362Z", "updatedAt": "2025-05-02T23:54:00.558Z", "documentId": "mnj6hszhbnvk0rl3ndo0s9lo", "locale": null, "publishedAt": "2025-08-11T18:59:08.171Z", "folderPath": null}, "logoHeader": null, "logoHeader2": null, "logoFooter": null, "homeBanner": null, "team": null, "notification": [], "badges": [], "showBadges": null, "customOnboardingForms": null, "onboarding": [], "printPhoto": null, "onboardingProcess": {"id": 1044, "slug": null, "completionPercent": null, "isSubmited": false, "lastFormVisited": "brokerInfo", "isLocked": false, "isComplete": false, "browserInfo": null, "submissionDate": null, "createdAt": "2025-09-08T21:01:30.864Z", "updatedAt": "2025-09-23T18:52:17.105Z", "publishedAt": "2025-09-23T18:52:17.246Z", "documentId": "kpoxfk2tymdfag4u2t2tgb0y", "locale": null}, "signature": {"id": 15606, "name": "applicantSignature-Bruno-unknown.png", "alternativeText": null, "caption": null, "width": 500, "height": 200, "formats": {"squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/squared/squared_applicant_Signature_Bruno_unknown_3b6b62f888.png", "hash": "squared_applicant_Signature_Bruno_unknown_3b6b62f888", "mime": "image/png", "name": "squared_applicantSignature-<PERSON> -unknown.png", "path": null, "size": 5.77, "width": 300, "height": 120, "sizeInBytes": 5768}, "thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/thumbnail/thumbnail_applicant_Signature_Bruno_unknown_3b6b62f888.png", "hash": "thumbnail_applicant_Signature_Bruno_unknown_3b6b62f888", "mime": "image/png", "name": "thumbnail_applicantSignature-<PERSON> -unknown.png", "path": null, "size": 4.46, "width": 245, "height": 98, "sizeInBytes": 4457}}, "hash": "applicant_Signature_Bruno_unknown_3b6b62f888", "ext": ".png", "mime": "image/png", "size": 1.69, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/origin/applicant_Signature_Bruno_unknown_3b6b62f888.png", "previewUrl": null, "provider": "@strapi/provider-upload-aws-s3", "provider_metadata": null, "createdAt": "2025-09-22T21:27:15.993Z", "updatedAt": "2025-09-22T21:27:15.993Z", "documentId": "neu399c7axfb6nk8v54av3y5", "locale": null, "publishedAt": "2025-09-22T21:27:15.994Z", "folderPath": "/1"}, "initials": null, "testimonialsList": [], "branch": null, "languages": [], "realtors": [], "events": [], "gifts": [], "listingSheets": [], "circularPhoto": {"id": 14613, "name": "1746217608669_sebvo-Bruno_Portrait-1_low.png", "alternativeText": null, "caption": null, "width": 300, "height": 300, "formats": {"small": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_small", "mime": "image/png", "name": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_small.png", "size": 30413, "width": 300}, "medium": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_medium", "mime": "image/png", "name": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_medium.png", "size": 150706, "width": 750}, "squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_squared", "mime": "image/png", "name": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_squared.png", "size": 30413, "width": 300, "height": 300}, "thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-thumbnail_1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f", "mime": "image/png", "name": "thumbnail_1746217608669_sebvo-Bruno_Portrait-1_low.png", "path": null, "size": 32.16, "width": 156, "height": 156, "formats": {"small": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-thumbnail_1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_small", "mime": "image/png", "name": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_small.png", "size": 33760, "width": 300}, "medium": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-thumbnail_1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_medium", "mime": "image/png", "name": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_medium.png", "size": 151251, "width": 750}, "squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-thumbnail_1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_squared", "mime": "image/png", "name": "thumbnail_1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f_squared.png", "size": 33760, "width": 300, "height": 300}}}}, "hash": "1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f", "ext": ".png", "mime": "image/png", "size": 35.23, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1746217608669_sebvo_<PERSON>_Portrait_1_low_d2e713aa5f-1746217608669_sebvo-<PERSON>_Portrait-1_low.png", "previewUrl": null, "provider": "aws-s3-resizing-and-optimisation", "provider_metadata": null, "createdAt": "2025-06-10T13:19:19.539Z", "updatedAt": "2025-06-10T13:19:20.272Z", "documentId": "jjy9e1nqycjr7nx19sk1fqna", "locale": null, "publishedAt": "2025-08-11T18:59:08.171Z", "folderPath": null}, "userProgresses": [], "trainingScores": [], "clientGiftingRequests": [], "taskRequest": null, "assistants": [], "leader": null, "logo": null, "logoNegative": null, "createdBy": {"id": 1, "firstname": "<PERSON>", "lastname": "Sousa", "username": "<EMAIL>", "email": "<EMAIL>", "password": "$2a$10$3GUndSk4CyJtvppBPTCpW.qspqmJtniESmSar.GTTqu53d.UiLq86", "resetPasswordToken": "5090566e9f4041144ca4b4beefea73448dfca459", "registrationToken": null, "isActive": true, "blocked": false, "preferedLanguage": null, "createdAt": null, "updatedAt": null, "documentId": "eg3jyrvzsa6y4g4zehhd1gq4", "locale": null, "publishedAt": "2025-08-11T18:59:08.760Z"}, "updatedBy": {"id": 1, "firstname": "<PERSON>", "lastname": "Sousa", "username": "<EMAIL>", "email": "<EMAIL>", "password": "$2a$10$3GUndSk4CyJtvppBPTCpW.qspqmJtniESmSar.GTTqu53d.UiLq86", "resetPasswordToken": "5090566e9f4041144ca4b4beefea73448dfca459", "registrationToken": null, "isActive": true, "blocked": false, "preferedLanguage": null, "createdAt": null, "updatedAt": null, "documentId": "eg3jyrvzsa6y4g4zehhd1gq4", "locale": null, "publishedAt": "2025-08-11T18:59:08.760Z"}, "localizations": []}, "brokerInfo": {"id": 702, "middlename": null, "lastname": null, "city": null, "workEmail": null, "cellPhone": null, "birthdate": null, "emergencyContact": null, "homePhone": null, "address": null, "sin": null, "postalCode": null, "additionalNotes": null, "firstname": null, "emergencyPhone": null, "bio": null, "province": null, "isFormComplete": false, "newlyLicensed": null, "tshirtSize": null, "firstSaveComplete": false, "workPhone": null, "lender1": null, "lender2": null, "lender3": null, "lender1Volume": null, "lender2Volume": null, "lender3Volume": null, "position": null, "titles": null, "legalName": null, "preferredName": null, "license": null, "declarationDetails": null, "hasSocialMedia": false, "hasFacebook": false, "hasInstagram": false, "hasLinkedin": false, "hasTwitter": false, "hasYoutube": false, "facebook": null, "instagram": null, "linkedin": null, "twitter": null, "youtube": null, "facebookHandler": null, "instagramHandler": null, "linkedinHandler": null, "twitterHandler": null, "youtubeHandler": null, "personalAddress": null, "personalCity": null, "personalProvince": null, "personalPostalCode": null, "suiteUnit": null, "personalSuiteUnit": null, "mortgageSoftware": null, "otherMortgageSoftware": null, "applicationLink": null, "hasTikTok": null, "tiktok": null, "tiktokHandler": null, "hasPinterest": null, "pinterest": null, "pinterestHandler": null, "hasThreads": null, "threads": null, "threadsHandler": null, "hasBluesky": null, "bluesky": null, "blueskyHandler": null, "declarationRegulatoryReview": null, "declarationRegulatoryReviewDetails": null, "declarationClientComplaints": null, "declarationClientComplaintsDetails": null, "signature": {"id": 15606, "name": "applicantSignature-Bruno-unknown.png", "alternativeText": null, "caption": null, "width": 500, "height": 200, "formats": {"squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/squared/squared_applicant_Signature_Bruno_unknown_3b6b62f888.png", "hash": "squared_applicant_Signature_Bruno_unknown_3b6b62f888", "mime": "image/png", "name": "squared_applicantSignature-<PERSON> -unknown.png", "path": null, "size": 5.77, "width": 300, "height": 120, "sizeInBytes": 5768}, "thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/thumbnail/thumbnail_applicant_Signature_Bruno_unknown_3b6b62f888.png", "hash": "thumbnail_applicant_Signature_Bruno_unknown_3b6b62f888", "mime": "image/png", "name": "thumbnail_applicantSignature-<PERSON> -unknown.png", "path": null, "size": 4.46, "width": 245, "height": 98, "sizeInBytes": 4457}}, "hash": "applicant_Signature_Bruno_unknown_3b6b62f888", "ext": ".png", "mime": "image/png", "size": 1.69, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/origin/applicant_Signature_Bruno_unknown_3b6b62f888.png", "previewUrl": null, "provider": "@strapi/provider-upload-aws-s3", "provider_metadata": null, "createdAt": "2025-09-22T21:27:15.993Z", "updatedAt": "2025-09-22T21:27:15.993Z", "documentId": "neu399c7axfb6nk8v54av3y5", "locale": null, "publishedAt": "2025-09-22T21:27:15.994Z", "folderPath": "/1"}, "pdfForm": null}, "mpcApplication": {"id": 608, "officeSuiteUnit": null, "middlename": null, "lastname": null, "gender": null, "alternateEmail": null, "applicantDeclarationDate": null, "representativePosition": null, "officeCity": null, "tollfree": null, "position": null, "city": null, "workEmail": null, "cellPhone": null, "employerDeclarationDate": null, "birthdate": null, "businessActivity": null, "officePostalCode": null, "address": null, "postalCode": null, "officeAddress": null, "website": null, "firstname": null, "suiteUnit": null, "workPhone": null, "fax": null, "officeWebsite": null, "ampDesignationOptIn": null, "ampDesignationFrenchOptIn": null, "province": null, "officeProvince": null, "isFormComplete": null, "firstSaveComplete": false, "preferredName": null, "declarationDetails": null, "declarationSuspended": null, "declarationLicenseDenied": null, "declarationFraud": null, "declarationBankruptcy": null, "declarationCriminalOffense": null, "applicantDeclarationSignature": {"id": 15603, "name": "applicantSignature-Bruno-unknown.png", "alternativeText": null, "caption": null, "width": 500, "height": 200, "formats": {"squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/squared/squared_applicant_Signature_Bruno_unknown_0ac8a90af1.png", "hash": "squared_applicant_Signature_Bruno_unknown_0ac8a90af1", "mime": "image/png", "name": "squared_applicantSignature-<PERSON> -unknown.png", "path": null, "size": 3.6, "width": 300, "height": 120, "sizeInBytes": 3604}, "thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/thumbnail/thumbnail_applicant_Signature_Bruno_unknown_0ac8a90af1.png", "hash": "thumbnail_applicant_Signature_Bruno_unknown_0ac8a90af1", "mime": "image/png", "name": "thumbnail_applicantSignature-<PERSON> -unknown.png", "path": null, "size": 2.75, "width": 245, "height": 98, "sizeInBytes": 2753}}, "hash": "applicant_Signature_Bruno_unknown_0ac8a90af1", "ext": ".png", "mime": "image/png", "size": 1.07, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/private/images/origin/applicant_Signature_Bruno_unknown_0ac8a90af1.png", "previewUrl": null, "provider": "@strapi/provider-upload-aws-s3", "provider_metadata": null, "createdAt": "2025-09-22T21:11:04.269Z", "updatedAt": "2025-09-22T21:11:04.269Z", "documentId": "t5l1cb4vjy2rxaf9wqflpdxw", "locale": null, "publishedAt": "2025-09-22T21:11:04.270Z", "folderPath": "/1"}, "employerDeclarationSignature": null, "pdfForm": null, "judgementAction": null}, "businessCardInfo": {"id": 604, "businessCardOptOut": null, "cardFrontStyle": null, "cardBackStyle": null, "titlesAfterName": null, "license": null, "workPhone": null, "cellPhone": null, "workEmail": null, "website": null, "address": null, "isFormComplete": null, "firstSaveComplete": false, "firstname": null, "middlename": null, "lastname": null, "position": null, "withPhoto": null, "facebookHandler": null, "instagramHandler": null, "linkedinHandler": null, "twitterHandler": null, "youtubeHandler": null, "qrCodeLink": null, "withQrCode": null, "pdfForm": null}, "websiteInfo": {"id": 592, "websiteOptIn": null, "ownDomain": null, "providedDomain": null, "isFormComplete": null, "websiteDomainName": null, "websiteDomainRegistrar": null, "firstSaveComplete": false, "additionalDomainNames": null, "priorWebsite": null, "priorWebsiteDomainNames": null, "otherRegistrar": false, "priorWebsitesUse": null, "pdfForm": null}, "letterOfDirection": {"id": 392, "isFormComplete": null, "firstSaveComplete": false, "acknowledgement": false, "pdfForm": null}, "paymentAuthorization": {"id": 638, "institutionNumber": null, "nameOnAccount": null, "bankName": null, "date": null, "chequingAccount": false, "savingsAccount": false, "accountNumber": null, "bankAddress": null, "transitNumber": null, "isFormComplete": null, "firstSaveComplete": false, "brokerName": null, "companyAccount": null, "businessNumber": null, "creditCardExpenses": null, "payrollRequired": null, "birthdate": null, "sin": null, "signature": null, "pdfForm": null, "articlesOfIncorporation": null}, "policiesAndProcedure": {"id": 652, "isFormComplete": null, "brokerName": null, "firstSaveComplete": null, "signatureDate": null, "pdfForm": null, "signature": null}, "contractAndSchedule": {"id": 584, "brokerName": null, "firstSaveComplete": null, "isFormComplete": null, "brokerSignatureDate": null, "witnessName": null, "brokerSignature": null, "brokerInitials": null, "contractFile": null, "pdfForm": null}, "photos": {"id": 666, "isFormComplete": null, "firstSaveComplete": null, "photo": null, "printPhoto": null, "pdfForm": null}, "unlicensedInfo": {"id": 130, "middlename": null, "lastname": null, "firstSaveComplete": null, "isFormComplete": null, "city": null, "workEmail": null, "cellPhone": null, "emergencyContact": null, "province": null, "address": null, "postalCode": null, "firstname": null, "suiteUnit": null, "emergencyPhone": null, "assistantTo": null, "completingCompliance": null, "personalAddress": null, "personalCity": null, "personalProvince": null, "personalSuiteUnit": null, "personalPostalCode": null, "pdfForm": null, "signature": null}, "unlicensedPolicies": null}, "meta": {}}